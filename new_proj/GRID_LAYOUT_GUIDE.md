# 表单设计器网格布局功能指南

## 🎯 功能概述

我们已经将表单设计器升级为支持灵活的行列网格布局系统，现在您可以：

1. **自定义行数**：根据需要添加多行
2. **自定义列数**：每行可以有1-6列
3. **字段跨列**：字段可以跨越多个列显示
4. **精确布局控制**：实现您描述的复杂布局需求

## 🏗 布局结构

### 数据结构变化
```javascript
// 新的行列结构
form: {
  id: null,
  name: "表单名称",
  rows: [
    {
      columns: 3,           // 这一行有3列
      fields: [
        [field1, field2],   // 第1列的字段数组
        [field3],           // 第2列的字段数组  
        [field4]            // 第3列的字段数组
      ]
    },
    {
      columns: 2,           // 这一行有2列
      fields: [
        [field5],           // 第1列的字段
        [field6]            // 第2列的字段
      ]
    }
  ]
}
```

### 字段跨列属性
```javascript
field: {
  id: "temp_validity",
  label: "临时有效期", 
  colSpan: 2,              // 跨越2列
  // ...其他属性
}
```

## 🎨 使用方法

### 1. 创建行列布局

#### 添加第一行
- 如果表单为空，点击"添加第一行"按钮
- 默认创建3列的行

#### 添加更多行
- 点击任意行的"添加行"按钮
- 新行会插入到当前行的下方

### 2. 调整列数

#### 修改行的列数
- 点击行头部的"调整列数"按钮
- 输入1-6之间的数字
- 系统会自动处理字段重新分配

#### 列数变化处理
- **增加列数**：自动添加空列
- **减少列数**：会提示处理多余字段

### 3. 拖拽字段到指定位置

#### 精确放置
- 从左侧字段库拖拽字段
- 放置到目标行的目标列中
- 每列可以包含多个字段

#### 空列提示
- 空列显示"拖拽字段到此处"提示
- 鼠标悬停时高亮显示

### 4. 配置字段跨列

#### 设置跨列
1. 选中要配置的字段
2. 在右侧属性面板找到"列跨度"选项
3. 选择1-4列的跨度
4. 字段会自动调整显示宽度

#### 跨列显示
- 跨列字段会显示"(跨X列)"标识
- 在网格中占据相应的列空间

## 📋 实际应用示例

### 示例1：您描述的布局需求

**第一行（3列）**：
- 列1：源IP (colSpan: 1)
- 列2：目标IP (colSpan: 1) 
- 列3：用途 (colSpan: 1)

**第二行（2列等宽）**：
- 列1：临时有效期 (colSpan: 2) - 跨越2列
- 列2：空（被临时有效期占用）

### 示例2：复杂表单布局

**第一行（4列）**：
- 列1：姓名
- 列2：性别
- 列3：年龄  
- 列4：电话

**第二行（2列）**：
- 列1：地址 (colSpan: 2) - 跨越2列
- 列2：空（被地址占用）

**第三行（3列）**：
- 列1：部门
- 列2：职位
- 列3：入职日期

## 🔧 操作技巧

### 1. 快速布局
- 先规划好行列结构
- 再拖拽字段到对应位置
- 最后调整跨列设置

### 2. 字段管理
- 每个字段都有独立的操作按钮
- 支持上移、下移、复制、删除
- 操作只在当前列内生效

### 3. 行管理
- 每行都有独立的操作区域
- 支持添加行、调整列数、删除行
- 删除行会同时删除行内所有字段

### 4. 预览功能
- 预览会按照实际的行列布局显示
- 跨列字段在预览中正确显示
- 保持响应式布局

## 🎯 高级功能

### 1. 响应式布局
- 在小屏幕上自动调整列数
- 保持良好的用户体验
- 跨列字段自动适应

### 2. 数据兼容性
- 自动转换旧版本的字段数组
- 向后兼容已保存的表单
- 平滑升级体验

### 3. 验证和提示
- 智能检测布局冲突
- 提供操作提示和确认
- 防止数据丢失

## 🚀 最佳实践

### 1. 布局规划
- 先在纸上或脑中规划布局
- 考虑字段的逻辑关系
- 合理使用跨列功能

### 2. 用户体验
- 相关字段放在同一行
- 重要字段使用跨列突出显示
- 保持视觉平衡

### 3. 性能优化
- 避免过多的行列嵌套
- 合理控制字段数量
- 定期清理无用字段

## 🔍 故障排除

### 1. 字段显示异常
- 检查colSpan设置是否超出列数
- 确认字段是否正确放置在列中
- 刷新页面重新加载

### 2. 拖拽不生效
- 确认目标区域是否为有效的拖放区
- 检查是否有其他元素遮挡
- 尝试先点击再拖拽

### 3. 布局错乱
- 检查CSS样式是否正确加载
- 确认浏览器兼容性
- 清除浏览器缓存

## 📝 注意事项

1. **列数限制**：每行最多支持6列
2. **跨列限制**：字段最多可跨4列
3. **数据保存**：布局信息会自动保存到localStorage
4. **浏览器兼容**：需要支持CSS Grid的现代浏览器
5. **性能考虑**：建议单个表单不超过50个字段

通过这个升级的网格布局系统，您现在可以创建任意复杂的表单布局，完全满足您描述的需求！
