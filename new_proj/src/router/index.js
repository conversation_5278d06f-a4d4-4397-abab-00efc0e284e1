import Vue from "vue";
import VueRouter from "vue-router";
import HomeView from "../views/HomeView.vue";

Vue.use(VueRouter);

const routes = [
  {
    path: "/",
    name: "home",
    component: HomeView,
  },
  {
    path: "/about",
    name: "about",
    // route level code-splitting
    // this generates a separate chunk (about.[hash].js) for this route
    // which is lazy-loaded when the route is visited.
    component: () =>
      import(/* webpackChunkName: "about" */ "../views/AboutView.vue"),
  },
  {
    path: "/ip-monitor",
    name: "ip-monitor",
    component: () =>
      import(/* webpackChunkName: "ip-monitor" */ "../views/IpMonitorView.vue"),
  },
  {
    path: "/system-monitor",
    name: "system-monitor",
    component: () =>
      import(
        /* webpackChunkName: "system-monitor" */ "../views/SystemMonitorView.vue"
      ),
  },
  {
    path: "/network-analysis",
    name: "network-analysis",
    component: () =>
      import(
        /* webpackChunkName: "network-analysis" */ "../views/NetworkAnalysisView.vue"
      ),
  },
  {
    path: "/log-management",
    name: "log-management",
    component: () =>
      import(
        /* webpackChunkName: "log-management" */ "../views/LogManagementView.vue"
      ),
  },
  {
    path: "/user-management",
    name: "user-management",
    component: () =>
      import(
        /* webpackChunkName: "user-management" */ "../views/UserManagementView.vue"
      ),
  },
  {
    path: "/settings",
    name: "settings",
    component: () =>
      import(/* webpackChunkName: "settings" */ "../views/SettingsView.vue"),
  },
];

const router = new VueRouter({
  mode: "history",
  base: process.env.BASE_URL,
  routes,
});

export default router;
