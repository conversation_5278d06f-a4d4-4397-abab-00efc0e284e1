<!--
  自定义表单设计器组件
  功能：
  1. 拖拽字段创建表单
  2. 字段属性配置
  3. 表单预览
  4. 表单保存和加载
  5. 支持多种字段类型：IP地址、日期范围、文本等
-->
<template>
  <div class="form-designer-component">
    <div class="designer-container">
      <!-- 左侧字段库 -->
      <div class="field-library">
        <div class="library-header">
          <h3>
            <i class="fas fa-th-list"></i>
            字段库
          </h3>
        </div>

        <div class="field-categories">
          <div class="category">
            <h4>基础字段</h4>
            <draggable
              v-model="availableFields"
              :group="{ name: 'fields', pull: 'clone', put: false }"
              :clone="cloneField"
              :sort="false"
              class="field-list"
            >
              <div
                v-for="field in availableFields"
                :key="field.id"
                class="field-item"
                :class="field.type"
              >
                <i :class="field.icon"></i>
                <span>{{ field.label }}</span>
                <small>{{ field.description }}</small>
              </div>
            </draggable>
          </div>
        </div>
      </div>

      <!-- 中间表单设计区域 -->
      <div class="form-canvas">
        <div class="canvas-header">
          <div class="form-info">
            <a-input
              v-model="form.name"
              placeholder="请输入表单名称"
              class="form-name-input"
            />
            <span class="field-count">{{ form.fields.length }} 个字段</span>
          </div>
          <div class="canvas-actions">
            <a-button @click="previewForm" type="primary">
              <i class="fas fa-eye"></i>
              预览
            </a-button>
            <a-button @click="saveForm" type="primary">
              <i class="fas fa-save"></i>
              保存
            </a-button>
            <a-button @click="clearForm">
              <i class="fas fa-trash"></i>
              清空
            </a-button>
          </div>
        </div>

        <!-- 表单画布 -->
        <div class="canvas-area">
          <div v-if="form.fields.length === 0" class="empty-canvas">
            <i class="fas fa-mouse-pointer"></i>
            <p>从左侧拖拽字段到这里开始设计表单</p>
          </div>

          <draggable
            v-model="form.fields"
            group="fields"
            class="form-fields"
            @add="onFieldAdd"
          >
            <div
              v-for="(field, index) in form.fields"
              :key="field.id"
              class="form-field"
              :class="{ active: selectedFieldIndex === index }"
              @click="selectField(index)"
            >
              <!-- 字段渲染 -->
              <div class="field-wrapper">
                <label class="field-label">
                  {{ field.label }}
                  <span v-if="field.required" class="required-mark">*</span>
                </label>

                <!-- 根据字段类型渲染不同的组件 -->
                <component
                  :is="getFieldComponent(field.type)"
                  v-bind="getFieldProps(field)"
                  :disabled="true"
                  class="field-component"
                />
              </div>

              <!-- 字段操作按钮 -->
              <div class="field-actions">
                <a-button
                  size="small"
                  @click.stop="moveFieldUp(index)"
                  :disabled="index === 0"
                >
                  <i class="fas fa-arrow-up"></i>
                </a-button>
                <a-button
                  size="small"
                  @click.stop="moveFieldDown(index)"
                  :disabled="index === form.fields.length - 1"
                >
                  <i class="fas fa-arrow-down"></i>
                </a-button>
                <a-button size="small" @click.stop="duplicateField(index)">
                  <i class="fas fa-copy"></i>
                </a-button>
                <a-button
                  size="small"
                  @click.stop="removeField(index)"
                  type="danger"
                >
                  <i class="fas fa-trash"></i>
                </a-button>
              </div>
            </div>
          </draggable>
        </div>
      </div>

      <!-- 右侧属性配置面板 -->
      <div class="property-panel">
        <div class="panel-header">
          <h3>
            <i class="fas fa-cog"></i>
            字段属性
          </h3>
        </div>

        <div class="panel-content">
          <div v-if="selectedField" class="field-properties">
            <!-- 基础属性 -->
            <div class="property-group">
              <h4>基础属性</h4>

              <div class="property-item">
                <label>字段标签</label>
                <a-input v-model="selectedField.label" />
              </div>

              <div class="property-item">
                <label>字段名称</label>
                <a-input v-model="selectedField.name" />
              </div>

              <div class="property-item">
                <label>占位符</label>
                <a-input v-model="selectedField.placeholder" />
              </div>

              <div class="property-item">
                <label>是否必填</label>
                <a-switch v-model="selectedField.required" />
              </div>
            </div>

            <!-- 样式属性 -->
            <div class="property-group">
              <h4>样式属性</h4>

              <div class="property-item">
                <label>字段宽度</label>
                <a-select v-model="selectedField.width" style="width: 100%">
                  <a-select-option value="25%">25%</a-select-option>
                  <a-select-option value="33%">33%</a-select-option>
                  <a-select-option value="50%">50%</a-select-option>
                  <a-select-option value="75%">75%</a-select-option>
                  <a-select-option value="100%">100%</a-select-option>
                </a-select>
              </div>
            </div>

            <!-- 特殊属性（根据字段类型显示） -->
            <div class="property-group" v-if="selectedField.type === 'select'">
              <h4>选项配置</h4>

              <div class="property-item">
                <label>选项列表</label>
                <div class="options-editor">
                  <div
                    v-for="(option, index) in selectedField.options"
                    :key="index"
                    class="option-item"
                  >
                    <a-input
                      v-model="option.label"
                      placeholder="选项标签"
                      style="margin-bottom: 8px"
                    />
                    <a-button
                      size="small"
                      @click="removeOption(index)"
                      type="danger"
                    >
                      删除
                    </a-button>
                  </div>
                  <a-button @click="addOption" type="dashed" block>
                    添加选项
                  </a-button>
                </div>
              </div>
            </div>
          </div>

          <div v-else class="no-selection">
            <i class="fas fa-hand-pointer"></i>
            <p>请选择一个字段进行配置</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 表单预览模态框 -->
    <a-modal
      v-model="previewVisible"
      title="表单预览"
      width="800px"
      :footer="null"
    >
      <div class="form-preview">
        <h3>{{ form.name || "未命名表单" }}</h3>
        <a-form :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-form-item
            v-for="field in form.fields"
            :key="field.id"
            :label="field.label"
            :required="field.required"
          >
            <component
              :is="getFieldComponent(field.type)"
              v-bind="getFieldProps(field)"
              :style="{ width: field.width }"
            />
          </a-form-item>
        </a-form>
      </div>
    </a-modal>
  </div>
</template>

<script>
import draggable from "vuedraggable";

export default {
  name: "FormDesigner",
  components: {
    draggable,
  },
  props: {
    // 初始表单数据
    initialForm: {
      type: Object,
      default: () => ({
        id: null,
        name: "",
        fields: [],
      }),
    },
  },
  data() {
    return {
      // 当前表单
      form: {
        id: null,
        name: "",
        fields: [],
      },

      // 选中的字段索引
      selectedFieldIndex: -1,

      // 预览模态框显示状态
      previewVisible: false,

      // 可用字段库
      availableFields: [
        {
          id: "source_ip",
          type: "ip",
          label: "源IP",
          name: "source_ip",
          placeholder: "请输入源IP地址",
          required: false,
          width: "100%",
          icon: "fas fa-network-wired",
          description: "点段式IP地址",
        },
        {
          id: "target_ip",
          type: "ip",
          label: "目标IP",
          name: "target_ip",
          placeholder: "请输入目标IP地址",
          required: false,
          width: "100%",
          icon: "fas fa-bullseye",
          description: "点段式IP地址",
        },
        {
          id: "temp_validity",
          type: "daterange",
          label: "临时有效期",
          name: "temp_validity",
          placeholder: "请选择有效期范围",
          required: false,
          width: "100%",
          icon: "fas fa-calendar-alt",
          description: "日期范围选择",
        },
        {
          id: "purpose",
          type: "textarea",
          label: "用途",
          name: "purpose",
          placeholder: "请输入用途说明",
          required: false,
          width: "100%",
          rows: 3,
          icon: "fas fa-edit",
          description: "多行文本",
        },
        {
          id: "open_time",
          type: "daterange",
          label: "开通时间",
          name: "open_time",
          placeholder: "请选择开通时间范围",
          required: false,
          width: "100%",
          icon: "fas fa-clock",
          description: "日期范围选择",
        },
      ],
    };
  },
  computed: {
    // 获取当前选中的字段
    selectedField() {
      if (
        this.selectedFieldIndex >= 0 &&
        this.selectedFieldIndex < this.form.fields.length
      ) {
        return this.form.fields[this.selectedFieldIndex];
      }
      return null;
    },
  },
  watch: {
    // 监听初始表单数据变化
    initialForm: {
      handler(newForm) {
        if (newForm) {
          this.form = {
            ...newForm,
            fields: newForm.fields
              ? newForm.fields.map((field) => ({ ...field }))
              : [],
          };
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    // 克隆字段（拖拽时使用）
    cloneField(field) {
      return {
        ...field,
        id: field.id + "_" + Date.now(), // 生成唯一ID
        name: field.name + "_" + Date.now(),
      };
    },

    // 字段添加到画布时的处理
    onFieldAdd(evt) {
      // 触发字段添加事件
      this.$emit("field-add", evt);
    },

    // 选择字段
    selectField(index) {
      this.selectedFieldIndex = index;
    },

    // 获取字段对应的组件名
    getFieldComponent(type) {
      const componentMap = {
        ip: "a-input",
        input: "a-input",
        textarea: "a-textarea",
        number: "a-input-number",
        select: "a-select",
        date: "a-date-picker",
        daterange: "a-range-picker",
        radio: "a-radio-group",
        checkbox: "a-checkbox-group",
      };
      return componentMap[type] || "a-input";
    },

    // 获取字段属性
    getFieldProps(field) {
      const baseProps = {
        placeholder: field.placeholder,
        style: { width: field.width },
      };

      // 根据字段类型添加特殊属性
      switch (field.type) {
        case "ip":
          return {
            ...baseProps,
            addonBefore: "IP",
          };
        case "number":
          return {
            ...baseProps,
            min: field.min || 0,
            max: field.max || 999999,
          };
        case "textarea":
          return {
            ...baseProps,
            rows: field.rows || 3,
          };
        case "select":
          return {
            ...baseProps,
            options: field.options || [],
          };
        case "daterange":
          return {
            ...baseProps,
            format: "YYYY-MM-DD",
          };
        default:
          return baseProps;
      }
    },

    // 上移字段
    moveFieldUp(index) {
      if (index > 0) {
        const fields = [...this.form.fields];
        const temp = fields[index];
        fields[index] = fields[index - 1];
        fields[index - 1] = temp;
        this.form.fields = fields;
        this.selectedFieldIndex = index - 1;
        this.emitFormChange();
      }
    },

    // 下移字段
    moveFieldDown(index) {
      if (index < this.form.fields.length - 1) {
        const fields = [...this.form.fields];
        const temp = fields[index];
        fields[index] = fields[index + 1];
        fields[index + 1] = temp;
        this.form.fields = fields;
        this.selectedFieldIndex = index + 1;
        this.emitFormChange();
      }
    },

    // 复制字段
    duplicateField(index) {
      const field = this.form.fields[index];
      const newField = {
        ...field,
        id: field.id + "_copy_" + Date.now(),
        name: field.name + "_copy",
        label: field.label + " (副本)",
      };
      this.form.fields.splice(index + 1, 0, newField);
      this.$message.success("字段已复制");
      this.emitFormChange();
    },

    // 删除字段
    removeField(index) {
      this.$confirm({
        title: "确认删除",
        content: "确定要删除这个字段吗？",
        onOk: () => {
          this.form.fields.splice(index, 1);
          if (this.selectedFieldIndex === index) {
            this.selectedFieldIndex = -1;
          } else if (this.selectedFieldIndex > index) {
            this.selectedFieldIndex--;
          }
          this.$message.success("字段已删除");
          this.emitFormChange();
        },
      });
    },

    // 预览表单
    previewForm() {
      if (this.form.fields.length === 0) {
        this.$message.warning("请先添加字段");
        return;
      }
      this.previewVisible = true;
    },

    // 保存表单
    saveForm() {
      if (!this.form.name.trim()) {
        this.$message.warning("请输入表单名称");
        return;
      }
      if (this.form.fields.length === 0) {
        this.$message.warning("请先添加字段");
        return;
      }

      const formToSave = {
        ...this.form,
        id: this.form.id || Date.now(),
        createTime: new Date().toLocaleString(),
        updateTime: new Date().toLocaleString(),
      };

      // 触发保存事件
      this.$emit("save-form", formToSave);
      this.$message.success("表单已保存");
    },

    // 清空表单
    clearForm() {
      this.$confirm({
        title: "确认清空",
        content: "确定要清空当前表单吗？",
        onOk: () => {
          this.form = {
            id: null,
            name: "",
            fields: [],
          };
          this.selectedFieldIndex = -1;
          this.$message.success("表单已清空");
          this.emitFormChange();
        },
      });
    },

    // 添加选项（用于下拉框等）
    addOption() {
      if (this.selectedField && this.selectedField.options) {
        this.selectedField.options.push({
          label: "新选项",
          value: "new_option_" + Date.now(),
        });
        this.emitFormChange();
      }
    },

    // 删除选项
    removeOption(index) {
      if (this.selectedField && this.selectedField.options) {
        this.selectedField.options.splice(index, 1);
        this.emitFormChange();
      }
    },

    // 触发表单变化事件
    emitFormChange() {
      this.$emit("form-change", this.form);
    },

    // 获取表单数据（供外部调用）
    getFormData() {
      return this.form;
    },

    // 设置表单数据（供外部调用）
    setFormData(formData) {
      this.form = {
        ...formData,
        fields: formData.fields
          ? formData.fields.map((field) => ({ ...field }))
          : [],
      };
      this.selectedFieldIndex = -1;
    },
  },
};
</script>

<style scoped>
/* 组件主容器 */
.form-designer-component {
  width: 100%;
  height: 100%;
}

/* 设计器容器 */
.designer-container {
  display: flex;
  gap: 20px;
  height: 100%;
}

/* 左侧字段库 */
.field-library {
  width: 280px;
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

.library-header h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.category h4 {
  margin: 0 0 15px 0;
  color: #34495e;
  font-size: 1rem;
  border-bottom: 2px solid #ecf0f1;
  padding-bottom: 8px;
}

.field-list {
  min-height: 50px;
}

.field-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 12px;
  margin-bottom: 8px;
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  cursor: grab;
  transition: all 0.3s ease;
}

.field-item:hover {
  background: #e3f2fd;
  border-color: #2196f3;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.2);
}

.field-item:active {
  cursor: grabbing;
}

.field-item i {
  color: #3498db;
  font-size: 1.1rem;
}

.field-item span {
  font-weight: 500;
  color: #2c3e50;
}

.field-item small {
  color: #7f8c8d;
  font-size: 0.85rem;
}

/* 中间表单画布 */
.form-canvas {
  flex: 1;
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.canvas-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #ecf0f1;
}

.form-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.form-name-input {
  width: 250px;
}

.field-count {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.canvas-actions {
  display: flex;
  gap: 10px;
}

.canvas-area {
  flex: 1;
  overflow-y: auto;
  min-height: 400px;
}

.empty-canvas {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #95a5a6;
  border: 2px dashed #bdc3c7;
  border-radius: 8px;
}

.empty-canvas i {
  font-size: 3rem;
  margin-bottom: 15px;
}

.empty-canvas p {
  font-size: 1.1rem;
  margin: 0;
}

.form-fields {
  min-height: 200px;
}

.form-field {
  position: relative;
  margin-bottom: 15px;
  padding: 15px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  background: #f8f9fa;
  transition: all 0.3s ease;
  cursor: pointer;
}

.form-field:hover {
  border-color: #3498db;
  background: #ffffff;
}

.form-field.active {
  border-color: #2980b9;
  background: #ffffff;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
}

.field-wrapper {
  margin-bottom: 10px;
}

.field-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #2c3e50;
}

.required-mark {
  color: #e74c3c;
  margin-left: 4px;
}

.field-component {
  width: 100%;
}

.field-actions {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  gap: 5px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.form-field:hover .field-actions,
.form-field.active .field-actions {
  opacity: 1;
}

/* 右侧属性面板 */
.property-panel {
  width: 320px;
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

.panel-header h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.panel-content {
  height: calc(100% - 60px);
}

.property-group {
  margin-bottom: 25px;
}

.property-group h4 {
  margin: 0 0 15px 0;
  color: #34495e;
  font-size: 1rem;
  border-bottom: 2px solid #ecf0f1;
  padding-bottom: 8px;
}

.property-item {
  margin-bottom: 15px;
}

.property-item label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #2c3e50;
  font-size: 0.9rem;
}

.no-selection {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #95a5a6;
}

.no-selection i {
  font-size: 2rem;
  margin-bottom: 10px;
}

.no-selection p {
  margin: 0;
  font-size: 0.9rem;
}

/* 选项编辑器 */
.options-editor {
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 10px;
  background: #f8f9fa;
}

.option-item {
  display: flex;
  gap: 8px;
  align-items: flex-start;
  margin-bottom: 8px;
}

.option-item:last-child {
  margin-bottom: 0;
}

/* 表单预览 */
.form-preview h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  text-align: center;
  padding-bottom: 15px;
  border-bottom: 2px solid #ecf0f1;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .designer-container {
    flex-direction: column;
    height: auto;
  }

  .field-library,
  .property-panel {
    width: 100%;
    max-height: 300px;
  }

  .form-canvas {
    min-height: 500px;
  }
}

@media (max-width: 768px) {
  .canvas-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .form-info {
    flex-direction: column;
    gap: 10px;
  }

  .form-name-input {
    width: 100%;
  }
}
</style>
