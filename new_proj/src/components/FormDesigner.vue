<!--
  自定义表单设计器组件
  功能：
  1. 拖拽字段创建表单
  2. 字段属性配置
  3. 表单预览
  4. 表单保存和加载
  5. 支持多种字段类型：IP地址、日期范围、文本等
-->
<template>
  <div class="form-designer-component">
    <div class="designer-container">
      <!-- 左侧字段库 -->
      <div class="field-library">
        <div class="library-header">
          <h3>
            <i class="fas fa-th-list"></i>
            字段库
          </h3>
        </div>

        <div class="field-categories">
          <div class="category">
            <h4>基础字段</h4>
            <draggable
              v-model="availableFields"
              :group="{ name: 'fields', pull: 'clone', put: false }"
              :clone="cloneField"
              :sort="false"
              class="field-list"
            >
              <div
                v-for="field in availableFields"
                :key="field.id"
                class="field-item"
                :class="field.type"
              >
                <i :class="field.icon"></i>
                <span>{{ field.label }}</span>
                <small>{{ field.description }}</small>
              </div>
            </draggable>
          </div>
        </div>
      </div>

      <!-- 中间表单设计区域 -->
      <div class="form-canvas">
        <div class="canvas-header">
          <div class="form-info">
            <a-input
              v-model="form.name"
              placeholder="请输入表单名称"
              class="form-name-input"
            />
            <span class="field-count">{{ allFields.length }} 个字段</span>
          </div>
          <div class="canvas-actions">
            <a-button @click="previewForm" type="primary">
              <i class="fas fa-eye"></i>
              预览
            </a-button>
            <a-button @click="saveForm" type="primary">
              <i class="fas fa-save"></i>
              保存
            </a-button>
            <a-button @click="clearForm">
              <i class="fas fa-trash"></i>
              清空
            </a-button>
          </div>
        </div>

        <!-- 表单画布 -->
        <div class="canvas-area">
          <div v-if="form.rows.length === 0" class="empty-canvas">
            <i class="fas fa-mouse-pointer"></i>
            <p>从左侧拖拽字段到这里开始设计表单，或点击下方按钮添加行</p>
            <a-button @click="addRow" type="dashed" style="margin-top: 15px">
              <i class="fas fa-plus"></i>
              添加第一行
            </a-button>
          </div>

          <!-- 行布局 -->
          <div v-else class="form-rows">
            <div
              v-for="(row, rowIndex) in form.rows"
              :key="'row-' + rowIndex"
              class="form-row"
              :class="{ 'active-row': selectedRowIndex === rowIndex }"
            >
              <!-- 行操作按钮 -->
              <div class="row-header">
                <span class="row-title"
                  >第 {{ rowIndex + 1 }} 行 ({{ row.columns }}列)</span
                >
                <div class="row-actions">
                  <a-button size="small" @click="addRow(rowIndex + 1)">
                    <i class="fas fa-plus"></i>
                    添加行
                  </a-button>
                  <a-button size="small" @click="changeRowColumns(rowIndex)">
                    <i class="fas fa-columns"></i>
                    调整列数
                  </a-button>
                  <a-button
                    size="small"
                    @click="removeRow(rowIndex)"
                    type="danger"
                    :disabled="form.rows.length === 1"
                  >
                    <i class="fas fa-trash"></i>
                    删除行
                  </a-button>
                </div>
              </div>

              <!-- 列布局 -->
              <div
                class="form-columns"
                :style="{ gridTemplateColumns: `repeat(${row.columns}, 1fr)` }"
              >
                <div
                  v-for="colIndex in row.columns"
                  :key="'col-' + rowIndex + '-' + colIndex"
                  class="form-column"
                >
                  <!-- 字段拖拽区域 -->
                  <draggable
                    v-model="row.fields[colIndex - 1]"
                    group="fields"
                    class="column-drop-zone"
                    :class="{
                      'has-field':
                        row.fields[colIndex - 1] &&
                        row.fields[colIndex - 1].length > 0,
                    }"
                    @add="(evt) => onFieldAdd(evt, rowIndex, colIndex - 1)"
                  >
                    <div
                      v-if="
                        !row.fields[colIndex - 1] ||
                        row.fields[colIndex - 1].length === 0
                      "
                      class="empty-column"
                    >
                      <i class="fas fa-plus-circle"></i>
                      <span>拖拽字段到此处</span>
                    </div>

                    <!-- 字段显示 -->
                    <div
                      v-for="(field, fieldIndex) in row.fields[colIndex - 1] ||
                      []"
                      :key="field.id"
                      class="form-field"
                      :class="{
                        active:
                          selectedFieldIndex ===
                          getFieldGlobalIndex(
                            rowIndex,
                            colIndex - 1,
                            fieldIndex
                          ),
                        [`span-${field.colSpan || 1}`]: field.colSpan > 1,
                      }"
                      :style="getFieldStyle(field, row.columns)"
                      @click="
                        selectField(
                          getFieldGlobalIndex(
                            rowIndex,
                            colIndex - 1,
                            fieldIndex
                          ),
                          rowIndex,
                          colIndex - 1,
                          fieldIndex
                        )
                      "
                    >
                      <!-- 字段渲染 -->
                      <div class="field-wrapper">
                        <label class="field-label">
                          {{ field.label }}
                          <span v-if="field.required" class="required-mark"
                            >*</span
                          >
                          <span v-if="field.colSpan > 1" class="span-indicator"
                            >(跨{{ field.colSpan }}列)</span
                          >
                        </label>

                        <!-- 根据字段类型渲染不同的组件 -->
                        <component
                          :is="getFieldComponent(field.type)"
                          v-bind="getFieldProps(field)"
                          :disabled="true"
                          class="field-component"
                        />
                      </div>

                      <!-- 字段操作按钮 -->
                      <div class="field-actions">
                        <a-button
                          size="small"
                          @click.stop="
                            moveFieldUp(rowIndex, colIndex - 1, fieldIndex)
                          "
                          :disabled="fieldIndex === 0"
                        >
                          <i class="fas fa-arrow-up"></i>
                        </a-button>
                        <a-button
                          size="small"
                          @click.stop="
                            moveFieldDown(rowIndex, colIndex - 1, fieldIndex)
                          "
                          :disabled="
                            fieldIndex ===
                            (row.fields[colIndex - 1] || []).length - 1
                          "
                        >
                          <i class="fas fa-arrow-down"></i>
                        </a-button>
                        <a-button
                          size="small"
                          @click.stop="
                            duplicateField(rowIndex, colIndex - 1, fieldIndex)
                          "
                        >
                          <i class="fas fa-copy"></i>
                        </a-button>
                        <a-button
                          size="small"
                          @click.stop="
                            removeField(rowIndex, colIndex - 1, fieldIndex)
                          "
                          type="danger"
                        >
                          <i class="fas fa-trash"></i>
                        </a-button>
                      </div>
                    </div>
                  </draggable>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧属性配置面板 -->
      <div class="property-panel">
        <div class="panel-header">
          <h3>
            <i class="fas fa-cog"></i>
            字段属性
          </h3>
        </div>

        <div class="panel-content">
          <div v-if="selectedField" class="field-properties">
            <!-- 基础属性 -->
            <div class="property-group">
              <h4>基础属性</h4>

              <div class="property-item">
                <label>字段标签</label>
                <a-input v-model="selectedField.label" />
              </div>

              <div class="property-item">
                <label>字段名称</label>
                <a-input v-model="selectedField.name" />
              </div>

              <div class="property-item">
                <label>占位符</label>
                <a-input v-model="selectedField.placeholder" />
              </div>

              <div class="property-item">
                <label>是否必填</label>
                <a-switch v-model="selectedField.required" />
              </div>
            </div>

            <!-- 样式属性 -->
            <div class="property-group">
              <h4>样式属性</h4>

              <div class="property-item">
                <label>列跨度</label>
                <a-select
                  v-model="selectedField.colSpan"
                  style="width: 100%"
                  @change="onColSpanChange"
                >
                  <a-select-option :value="1">1列</a-select-option>
                  <a-select-option :value="2">2列</a-select-option>
                  <a-select-option :value="3">3列</a-select-option>
                  <a-select-option :value="4">4列</a-select-option>
                </a-select>
                <small style="color: #666; margin-top: 5px; display: block">
                  字段将跨越指定数量的列
                </small>
              </div>

              <div class="property-item">
                <label>字段宽度</label>
                <a-select v-model="selectedField.width" style="width: 100%">
                  <a-select-option value="25%">25%</a-select-option>
                  <a-select-option value="33%">33%</a-select-option>
                  <a-select-option value="50%">50%</a-select-option>
                  <a-select-option value="75%">75%</a-select-option>
                  <a-select-option value="100%">100%</a-select-option>
                </a-select>
              </div>
            </div>

            <!-- 特殊属性（根据字段类型显示） -->
            <div class="property-group" v-if="selectedField.type === 'select'">
              <h4>选项配置</h4>

              <div class="property-item">
                <label>选项列表</label>
                <div class="options-editor">
                  <div
                    v-for="(option, index) in selectedField.options"
                    :key="index"
                    class="option-item"
                  >
                    <a-input
                      v-model="option.label"
                      placeholder="选项标签"
                      style="margin-bottom: 8px"
                    />
                    <a-button
                      size="small"
                      @click="removeOption(index)"
                      type="danger"
                    >
                      删除
                    </a-button>
                  </div>
                  <a-button @click="addOption" type="dashed" block>
                    添加选项
                  </a-button>
                </div>
              </div>
            </div>
          </div>

          <div v-else class="no-selection">
            <i class="fas fa-hand-pointer"></i>
            <p>请选择一个字段进行配置</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 表单预览模态框 -->
    <a-modal
      v-model="previewVisible"
      title="表单预览"
      width="800px"
      :footer="null"
    >
      <div class="form-preview">
        <h3>{{ form.name || "未命名表单" }}</h3>
        <a-form :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <!-- 按行列布局显示 -->
          <div
            v-for="(row, rowIndex) in form.rows"
            :key="'preview-row-' + rowIndex"
            class="preview-row"
          >
            <div
              class="preview-columns"
              :style="{
                display: 'grid',
                gridTemplateColumns: `repeat(${row.columns}, 1fr)`,
                gap: '15px',
              }"
            >
              <div
                v-for="colIndex in row.columns"
                :key="'preview-col-' + colIndex"
                class="preview-column"
              >
                <a-form-item
                  v-for="field in row.fields[colIndex - 1] || []"
                  :key="field.id"
                  :label="field.label"
                  :required="field.required"
                  :style="getFieldStyle(field, row.columns)"
                >
                  <component
                    :is="getFieldComponent(field.type)"
                    v-bind="getFieldProps(field)"
                  />
                </a-form-item>
              </div>
            </div>
          </div>
        </a-form>
      </div>
    </a-modal>
  </div>
</template>

<script>
import draggable from "vuedraggable";

export default {
  name: "FormDesigner",
  components: {
    draggable,
  },
  props: {
    // 初始表单数据
    initialForm: {
      type: Object,
      default: () => ({
        id: null,
        name: "",
        fields: [],
      }),
    },
  },
  data() {
    return {
      // 当前表单
      form: {
        id: null,
        name: "",
        rows: [], // 改为行列结构
      },

      // 选中的字段索引
      selectedFieldIndex: -1,
      selectedRowIndex: -1,
      selectedColIndex: -1,
      selectedFieldInColIndex: -1,

      // 预览模态框显示状态
      previewVisible: false,

      // 可用字段库
      availableFields: [
        {
          id: "source_ip",
          type: "ip",
          label: "源IP",
          name: "source_ip",
          placeholder: "请输入源IP地址",
          required: false,
          width: "100%",
          icon: "fas fa-network-wired",
          description: "点段式IP地址",
        },
        {
          id: "target_ip",
          type: "ip",
          label: "目标IP",
          name: "target_ip",
          placeholder: "请输入目标IP地址",
          required: false,
          width: "100%",
          icon: "fas fa-bullseye",
          description: "点段式IP地址",
        },
        {
          id: "temp_validity",
          type: "daterange",
          label: "临时有效期",
          name: "temp_validity",
          placeholder: "请选择有效期范围",
          required: false,
          width: "100%",
          icon: "fas fa-calendar-alt",
          description: "日期范围选择",
        },
        {
          id: "purpose",
          type: "textarea",
          label: "用途",
          name: "purpose",
          placeholder: "请输入用途说明",
          required: false,
          width: "100%",
          rows: 3,
          icon: "fas fa-edit",
          description: "多行文本",
        },
        {
          id: "open_time",
          type: "daterange",
          label: "开通时间",
          name: "open_time",
          placeholder: "请选择开通时间范围",
          required: false,
          width: "100%",
          icon: "fas fa-clock",
          description: "日期范围选择",
        },
      ],
    };
  },
  computed: {
    // 获取当前选中的字段
    selectedField() {
      if (
        this.selectedRowIndex >= 0 &&
        this.selectedColIndex >= 0 &&
        this.selectedFieldInColIndex >= 0 &&
        this.form.rows[this.selectedRowIndex] &&
        this.form.rows[this.selectedRowIndex].fields[this.selectedColIndex] &&
        this.form.rows[this.selectedRowIndex].fields[this.selectedColIndex][
          this.selectedFieldInColIndex
        ]
      ) {
        return this.form.rows[this.selectedRowIndex].fields[
          this.selectedColIndex
        ][this.selectedFieldInColIndex];
      }
      return null;
    },

    // 获取所有字段的扁平化列表（用于预览）
    allFields() {
      const fields = [];
      this.form.rows.forEach((row) => {
        row.fields.forEach((colFields) => {
          if (colFields) {
            fields.push(...colFields);
          }
        });
      });
      return fields;
    },
  },
  watch: {
    // 监听初始表单数据变化
    initialForm: {
      handler(newForm) {
        if (newForm) {
          this.form = {
            ...newForm,
            rows:
              newForm.rows ||
              (newForm.fields ? this.convertFieldsToRows(newForm.fields) : []),
          };
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    // 克隆字段（拖拽时使用）
    cloneField(field) {
      return {
        ...field,
        id: field.id + "_" + Date.now(), // 生成唯一ID
        name: field.name + "_" + Date.now(),
        colSpan: 1, // 默认跨1列
      };
    },

    // 字段添加到画布时的处理
    onFieldAdd(evt, rowIndex, colIndex) {
      // 确保字段数组存在
      if (!this.form.rows[rowIndex].fields[colIndex]) {
        this.$set(this.form.rows[rowIndex].fields, colIndex, []);
      }
      // 触发字段添加事件
      this.$emit("field-add", evt);
      this.emitFormChange();
    },

    // 选择字段
    selectField(globalIndex, rowIndex, colIndex, fieldIndex) {
      this.selectedFieldIndex = globalIndex;
      this.selectedRowIndex = rowIndex;
      this.selectedColIndex = colIndex;
      this.selectedFieldInColIndex = fieldIndex;
    },

    // 获取字段的全局索引
    getFieldGlobalIndex(rowIndex, colIndex, fieldIndex) {
      let globalIndex = 0;
      for (let r = 0; r < rowIndex; r++) {
        const row = this.form.rows[r];
        row.fields.forEach((colFields) => {
          if (colFields) {
            globalIndex += colFields.length;
          }
        });
      }
      for (let c = 0; c < colIndex; c++) {
        const colFields = this.form.rows[rowIndex].fields[c];
        if (colFields) {
          globalIndex += colFields.length;
        }
      }
      return globalIndex + fieldIndex;
    },

    // 获取字段样式
    getFieldStyle(field, totalColumns) {
      const colSpan = field.colSpan || 1;
      if (colSpan > 1) {
        return {
          gridColumn: `span ${Math.min(colSpan, totalColumns)}`,
        };
      }
      return {};
    },

    // 添加行
    addRow(insertIndex = null) {
      const newRow = {
        columns: 3, // 默认3列
        fields: [[], [], []], // 初始化3个空列
      };

      if (insertIndex !== null) {
        this.form.rows.splice(insertIndex, 0, newRow);
      } else {
        this.form.rows.push(newRow);
      }
      this.emitFormChange();
    },

    // 删除行
    removeRow(rowIndex) {
      this.$confirm({
        title: "确认删除",
        content: "确定要删除这一行吗？行中的所有字段也会被删除。",
        onOk: () => {
          this.form.rows.splice(rowIndex, 1);
          this.selectedFieldIndex = -1;
          this.selectedRowIndex = -1;
          this.selectedColIndex = -1;
          this.selectedFieldInColIndex = -1;
          this.emitFormChange();
          this.$message.success("行已删除");
        },
      });
    },

    // 调整行的列数
    changeRowColumns(rowIndex) {
      const row = this.form.rows[rowIndex];
      this.$prompt("请输入列数 (1-6)", "调整列数", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputValue: row.columns.toString(),
        inputValidator: (value) => {
          const num = parseInt(value);
          if (isNaN(num) || num < 1 || num > 6) {
            return "请输入1-6之间的数字";
          }
          return true;
        },
      })
        .then(({ value }) => {
          const newColumns = parseInt(value);
          const oldColumns = row.columns;

          if (newColumns !== oldColumns) {
            // 调整字段数组
            if (newColumns > oldColumns) {
              // 增加列
              for (let i = oldColumns; i < newColumns; i++) {
                row.fields.push([]);
              }
            } else {
              // 减少列，需要处理多余的字段
              const removedFields = [];
              for (let i = newColumns; i < oldColumns; i++) {
                if (row.fields[i] && row.fields[i].length > 0) {
                  removedFields.push(...row.fields[i]);
                }
              }

              // 如果有字段被移除，询问用户如何处理
              if (removedFields.length > 0) {
                this.$confirm({
                  title: "字段处理",
                  content: `减少列数会移除 ${removedFields.length} 个字段，是否继续？`,
                  onOk: () => {
                    row.fields = row.fields.slice(0, newColumns);
                    row.columns = newColumns;
                    this.emitFormChange();
                    this.$message.success("列数已调整");
                  },
                });
                return;
              } else {
                row.fields = row.fields.slice(0, newColumns);
              }
            }

            row.columns = newColumns;
            this.emitFormChange();
            this.$message.success("列数已调整");
          }
        })
        .catch(() => {
          // 用户取消
        });
    },

    // 转换旧的字段数组为行列结构
    convertFieldsToRows(fields) {
      if (!fields || fields.length === 0) return [];

      // 简单地将所有字段放在一行中
      const row = {
        columns: Math.min(fields.length, 4), // 最多4列
        fields: [],
      };

      // 初始化列数组
      for (let i = 0; i < row.columns; i++) {
        row.fields.push([]);
      }

      // 分配字段到列中
      fields.forEach((field, index) => {
        const colIndex = index % row.columns;
        row.fields[colIndex].push({
          ...field,
          colSpan: 1,
        });
      });

      return [row];
    },

    // 获取字段对应的组件名
    getFieldComponent(type) {
      const componentMap = {
        ip: "a-input",
        input: "a-input",
        textarea: "a-textarea",
        number: "a-input-number",
        select: "a-select",
        date: "a-date-picker",
        daterange: "a-range-picker",
        radio: "a-radio-group",
        checkbox: "a-checkbox-group",
      };
      return componentMap[type] || "a-input";
    },

    // 获取字段属性
    getFieldProps(field) {
      const baseProps = {
        placeholder: field.placeholder,
        style: { width: field.width },
      };

      // 根据字段类型添加特殊属性
      switch (field.type) {
        case "ip":
          return {
            ...baseProps,
            addonBefore: "IP",
          };
        case "number":
          return {
            ...baseProps,
            min: field.min || 0,
            max: field.max || 999999,
          };
        case "textarea":
          return {
            ...baseProps,
            rows: field.rows || 3,
          };
        case "select":
          return {
            ...baseProps,
            options: field.options || [],
          };
        case "daterange":
          return {
            ...baseProps,
            format: "YYYY-MM-DD",
          };
        default:
          return baseProps;
      }
    },

    // 上移字段
    moveFieldUp(rowIndex, colIndex, fieldIndex) {
      if (fieldIndex > 0) {
        const colFields = this.form.rows[rowIndex].fields[colIndex];
        const temp = colFields[fieldIndex];
        colFields[fieldIndex] = colFields[fieldIndex - 1];
        colFields[fieldIndex - 1] = temp;
        this.selectedFieldInColIndex = fieldIndex - 1;
        this.emitFormChange();
      }
    },

    // 下移字段
    moveFieldDown(rowIndex, colIndex, fieldIndex) {
      const colFields = this.form.rows[rowIndex].fields[colIndex];
      if (fieldIndex < colFields.length - 1) {
        const temp = colFields[fieldIndex];
        colFields[fieldIndex] = colFields[fieldIndex + 1];
        colFields[fieldIndex + 1] = temp;
        this.selectedFieldInColIndex = fieldIndex + 1;
        this.emitFormChange();
      }
    },

    // 复制字段
    duplicateField(rowIndex, colIndex, fieldIndex) {
      const field = this.form.rows[rowIndex].fields[colIndex][fieldIndex];
      const newField = {
        ...field,
        id: field.id + "_copy_" + Date.now(),
        name: field.name + "_copy",
        label: field.label + " (副本)",
      };
      this.form.rows[rowIndex].fields[colIndex].splice(
        fieldIndex + 1,
        0,
        newField
      );
      this.$message.success("字段已复制");
      this.emitFormChange();
    },

    // 删除字段
    removeField(rowIndex, colIndex, fieldIndex) {
      this.$confirm({
        title: "确认删除",
        content: "确定要删除这个字段吗？",
        onOk: () => {
          this.form.rows[rowIndex].fields[colIndex].splice(fieldIndex, 1);
          this.selectedFieldIndex = -1;
          this.selectedRowIndex = -1;
          this.selectedColIndex = -1;
          this.selectedFieldInColIndex = -1;
          this.$message.success("字段已删除");
          this.emitFormChange();
        },
      });
    },

    // 列跨度变化处理
    onColSpanChange() {
      this.emitFormChange();
    },

    // 预览表单
    previewForm() {
      if (this.allFields.length === 0) {
        this.$message.warning("请先添加字段");
        return;
      }
      this.previewVisible = true;
    },

    // 保存表单
    saveForm() {
      if (!this.form.name.trim()) {
        this.$message.warning("请输入表单名称");
        return;
      }
      if (this.allFields.length === 0) {
        this.$message.warning("请先添加字段");
        return;
      }

      const formToSave = {
        ...this.form,
        id: this.form.id || Date.now(),
        createTime: new Date().toLocaleString(),
        updateTime: new Date().toLocaleString(),
      };

      // 触发保存事件
      this.$emit("save-form", formToSave);
      this.$message.success("表单已保存");
    },

    // 清空表单
    clearForm() {
      this.$confirm({
        title: "确认清空",
        content: "确定要清空当前表单吗？",
        onOk: () => {
          this.form = {
            id: null,
            name: "",
            rows: [],
          };
          this.selectedFieldIndex = -1;
          this.selectedRowIndex = -1;
          this.selectedColIndex = -1;
          this.selectedFieldInColIndex = -1;
          this.$message.success("表单已清空");
          this.emitFormChange();
        },
      });
    },

    // 添加选项（用于下拉框等）
    addOption() {
      if (this.selectedField && this.selectedField.options) {
        this.selectedField.options.push({
          label: "新选项",
          value: "new_option_" + Date.now(),
        });
        this.emitFormChange();
      }
    },

    // 删除选项
    removeOption(index) {
      if (this.selectedField && this.selectedField.options) {
        this.selectedField.options.splice(index, 1);
        this.emitFormChange();
      }
    },

    // 触发表单变化事件
    emitFormChange() {
      this.$emit("form-change", this.form);
    },

    // 获取表单数据（供外部调用）
    getFormData() {
      return this.form;
    },

    // 设置表单数据（供外部调用）
    setFormData(formData) {
      this.form = {
        ...formData,
        rows:
          formData.rows ||
          (formData.fields ? this.convertFieldsToRows(formData.fields) : []),
      };
      this.selectedFieldIndex = -1;
      this.selectedRowIndex = -1;
      this.selectedColIndex = -1;
      this.selectedFieldInColIndex = -1;
    },
  },
};
</script>

<style scoped>
/* 组件主容器 */
.form-designer-component {
  width: 100%;
  height: 100%;
}

/* 设计器容器 */
.designer-container {
  display: flex;
  gap: 20px;
  height: 100%;
}

/* 左侧字段库 */
.field-library {
  width: 280px;
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

.library-header h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.category h4 {
  margin: 0 0 15px 0;
  color: #34495e;
  font-size: 1rem;
  border-bottom: 2px solid #ecf0f1;
  padding-bottom: 8px;
}

.field-list {
  min-height: 50px;
}

.field-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 12px;
  margin-bottom: 8px;
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  cursor: grab;
  transition: all 0.3s ease;
}

.field-item:hover {
  background: #e3f2fd;
  border-color: #2196f3;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.2);
}

.field-item:active {
  cursor: grabbing;
}

.field-item i {
  color: #3498db;
  font-size: 1.1rem;
}

.field-item span {
  font-weight: 500;
  color: #2c3e50;
}

.field-item small {
  color: #7f8c8d;
  font-size: 0.85rem;
}

/* 中间表单画布 */
.form-canvas {
  flex: 1;
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.canvas-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #ecf0f1;
}

.form-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.form-name-input {
  width: 250px;
}

.field-count {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.canvas-actions {
  display: flex;
  gap: 10px;
}

.canvas-area {
  flex: 1;
  overflow-y: auto;
  min-height: 400px;
}

.empty-canvas {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #95a5a6;
  border: 2px dashed #bdc3c7;
  border-radius: 8px;
}

.empty-canvas i {
  font-size: 3rem;
  margin-bottom: 15px;
}

.empty-canvas p {
  font-size: 1.1rem;
  margin: 0;
}

/* 行列布局样式 */
.form-rows {
  min-height: 200px;
}

.form-row {
  margin-bottom: 20px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  background: #ffffff;
  transition: all 0.3s ease;
}

.form-row.active-row {
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.row-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  border-radius: 6px 6px 0 0;
}

.row-title {
  font-weight: 500;
  color: #2c3e50;
}

.row-actions {
  display: flex;
  gap: 8px;
}

.form-columns {
  display: grid;
  gap: 15px;
  padding: 15px;
}

.form-column {
  min-height: 80px;
}

.column-drop-zone {
  min-height: 80px;
  border: 2px dashed #bdc3c7;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.column-drop-zone.has-field {
  border-style: solid;
  border-color: #e9ecef;
}

.column-drop-zone:hover {
  border-color: #3498db;
  background: rgba(52, 152, 219, 0.05);
}

.empty-column {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 80px;
  color: #95a5a6;
  font-size: 0.9rem;
}

.empty-column i {
  font-size: 1.5rem;
  margin-bottom: 5px;
}

.form-fields {
  min-height: 200px;
}

.form-field {
  position: relative;
  margin-bottom: 15px;
  padding: 15px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  background: #f8f9fa;
  transition: all 0.3s ease;
  cursor: pointer;
}

.form-field:hover {
  border-color: #3498db;
  background: #ffffff;
}

.form-field.active {
  border-color: #2980b9;
  background: #ffffff;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
}

.field-wrapper {
  margin-bottom: 10px;
}

.field-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #2c3e50;
}

.required-mark {
  color: #e74c3c;
  margin-left: 4px;
}

.field-component {
  width: 100%;
}

.field-actions {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  gap: 5px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.form-field:hover .field-actions,
.form-field.active .field-actions {
  opacity: 1;
}

/* 跨列字段样式 */
.form-field.span-2 {
  grid-column: span 2;
}

.form-field.span-3 {
  grid-column: span 3;
}

.form-field.span-4 {
  grid-column: span 4;
}

.span-indicator {
  color: #3498db;
  font-size: 0.8rem;
  font-weight: normal;
  margin-left: 8px;
}

/* 预览样式 */
.preview-row {
  margin-bottom: 20px;
}

.preview-columns {
  margin-bottom: 15px;
}

/* 右侧属性面板 */
.property-panel {
  width: 320px;
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

.panel-header h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.panel-content {
  height: calc(100% - 60px);
}

.property-group {
  margin-bottom: 25px;
}

.property-group h4 {
  margin: 0 0 15px 0;
  color: #34495e;
  font-size: 1rem;
  border-bottom: 2px solid #ecf0f1;
  padding-bottom: 8px;
}

.property-item {
  margin-bottom: 15px;
}

.property-item label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #2c3e50;
  font-size: 0.9rem;
}

.no-selection {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #95a5a6;
}

.no-selection i {
  font-size: 2rem;
  margin-bottom: 10px;
}

.no-selection p {
  margin: 0;
  font-size: 0.9rem;
}

/* 选项编辑器 */
.options-editor {
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 10px;
  background: #f8f9fa;
}

.option-item {
  display: flex;
  gap: 8px;
  align-items: flex-start;
  margin-bottom: 8px;
}

.option-item:last-child {
  margin-bottom: 0;
}

/* 表单预览 */
.form-preview h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  text-align: center;
  padding-bottom: 15px;
  border-bottom: 2px solid #ecf0f1;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .designer-container {
    flex-direction: column;
    height: auto;
  }

  .field-library,
  .property-panel {
    width: 100%;
    max-height: 300px;
  }

  .form-canvas {
    min-height: 500px;
  }
}

@media (max-width: 768px) {
  .canvas-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .form-info {
    flex-direction: column;
    gap: 10px;
  }

  .form-name-input {
    width: 100%;
  }
}
</style>
