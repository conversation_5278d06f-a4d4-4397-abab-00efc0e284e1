<template>
  <div class="form-designer">
    <!-- 页面头部 -->
    <div class="page-header">
      <button class="back-btn" @click="goBack">
        <i class="fas fa-arrow-left"></i>
        返回主页
      </button>
      <h1 class="page-title">自定义表单设计器</h1>
      <p class="page-subtitle">拖拽字段创建自定义表单</p>
    </div>

    <!-- 表单设计器组件 -->
    <FormDesigner
      ref="formDesigner"
      :initial-form="currentForm"
      @save-form="handleSaveForm"
      @form-change="handleFormChange"
      @field-add="handleFieldAdd"
    />

    <!-- 保存的表单列表 -->
    <div class="saved-forms" v-if="savedForms.length > 0">
      <h3>
        <i class="fas fa-folder-open"></i>
        已保存的表单 ({{ savedForms.length }})
      </h3>
      <div class="forms-grid">
        <div
          v-for="form in savedForms"
          :key="form.id"
          class="form-card"
          @click="loadForm(form)"
        >
          <div class="form-card-header">
            <h4>{{ form.name }}</h4>
            <div class="form-meta">
              <span class="field-count">
                <i class="fas fa-list"></i>
                {{ getFormFieldCount(form) }} 个字段
              </span>
              <span class="row-count" v-if="form.rows">
                <i class="fas fa-th-large"></i>
                {{ form.rows.length }} 行
              </span>
            </div>
            <div class="form-time">
              <small>创建: {{ form.createTime }}</small>
              <small v-if="form.updateTime !== form.createTime">
                更新: {{ form.updateTime }}
              </small>
            </div>
          </div>
          <div class="form-card-actions">
            <a-button size="small" @click.stop="loadForm(form)" type="primary">
              <i class="fas fa-edit"></i>
              编辑
            </a-button>
            <a-button size="small" @click.stop="duplicateForm(form)">
              <i class="fas fa-copy"></i>
              复制
            </a-button>
            <a-button
              size="small"
              @click.stop="deleteForm(form.id)"
              type="danger"
            >
              <i class="fas fa-trash"></i>
              删除
            </a-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import FormDesigner from "@/components/FormDesigner.vue";

export default {
  name: "SystemMonitorView",
  components: {
    FormDesigner,
  },
  data() {
    return {
      // 当前正在设计的表单
      currentForm: {
        id: null,
        name: "",
        fields: [],
      },

      // 已保存的表单列表
      savedForms: [],
    };
  },
  mounted() {
    // 从localStorage加载已保存的表单
    this.loadSavedForms();
  },
  methods: {
    // 返回主页
    goBack() {
      this.$router.push("/");
    },

    // 处理表单保存
    handleSaveForm(formData) {
      // 检查是否是更新现有表单
      const existingIndex = this.savedForms.findIndex(
        (f) => f.id === formData.id
      );
      if (existingIndex >= 0) {
        this.savedForms[existingIndex] = formData;
        this.$message.success("表单已更新");
      } else {
        this.savedForms.push(formData);
        this.$message.success("表单已保存");
      }

      // 保存到localStorage
      this.saveForms();
    },

    // 处理表单变化
    handleFormChange(formData) {
      this.currentForm = formData;
    },

    // 处理字段添加
    handleFieldAdd(evt) {
      console.log("字段已添加:", evt);
    },

    // 获取表单字段数量
    getFormFieldCount(form) {
      if (form.rows) {
        // 新的行列结构
        let count = 0;
        form.rows.forEach((row) => {
          row.fields.forEach((colFields) => {
            if (colFields) {
              count += colFields.length;
            }
          });
        });
        return count;
      } else if (form.fields) {
        // 旧的字段数组结构
        return form.fields.length;
      }
      return 0;
    },

    // 从localStorage加载已保存的表单
    loadSavedForms() {
      try {
        const saved = localStorage.getItem("customForms");
        if (saved) {
          this.savedForms = JSON.parse(saved);
        }
      } catch (error) {
        console.error("加载表单失败:", error);
      }
    },

    // 保存表单到localStorage
    saveForms() {
      try {
        localStorage.setItem("customForms", JSON.stringify(this.savedForms));
      } catch (error) {
        console.error("保存表单失败:", error);
        this.$message.error("保存失败");
      }
    },

    // 加载表单
    loadForm(form) {
      this.currentForm = {
        ...form,
        fields: form.fields.map((field) => ({ ...field })), // 深拷贝字段
      };
      // 通知FormDesigner组件更新
      this.$refs.formDesigner.setFormData(this.currentForm);
      this.$message.success(`已加载表单: ${form.name}`);
    },

    // 复制表单
    duplicateForm(form) {
      const newForm = {
        ...form,
        id: Date.now(),
        name: form.name + " (副本)",
        createTime: new Date().toLocaleString(),
        updateTime: new Date().toLocaleString(),
        fields: form.fields.map((field) => ({ ...field })),
      };
      this.savedForms.push(newForm);
      this.saveForms();
      this.$message.success("表单已复制");
    },

    // 删除表单
    deleteForm(formId) {
      this.$confirm({
        title: "确认删除",
        content: "确定要删除这个表单吗？",
        onOk: () => {
          this.savedForms = this.savedForms.filter((f) => f.id !== formId);
          this.saveForms();
          this.$message.success("表单已删除");
        },
      });
    },
  },
};
</script>

<style scoped>
/* 主容器样式 */
.form-designer {
  padding: 20px;
  height: 100vh;
  background: #f5f6fa;
  overflow: hidden;
}

/* 页面头部样式 */
.page-header {
  margin-bottom: 20px;
}

.back-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  margin-bottom: 20px;
  transition: background 0.3s ease;
}

.back-btn:hover {
  background: #2980b9;
}

.back-btn i {
  margin-right: 8px;
}

.page-title {
  font-size: 2.5rem;
  color: #2c3e50;
  margin: 0 0 10px 0;
  font-weight: 600;
}

.page-subtitle {
  font-size: 1.2rem;
  color: #7f8c8d;
  margin: 0;
}

/* 保存的表单列表样式 */
.saved-forms {
  margin-top: 30px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.saved-forms h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 1.3rem;
  display: flex;
  align-items: center;
  gap: 10px;
}

.saved-forms h3 i {
  color: #3498db;
}

.forms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.form-card {
  padding: 20px;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.form-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3498db, #2ecc71);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.form-card:hover::before {
  opacity: 1;
}

.form-card:hover {
  border-color: #3498db;
  background: #ffffff;
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(52, 152, 219, 0.15);
}

.form-card-header {
  margin-bottom: 15px;
}

.form-card-header h4 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-size: 1.2rem;
  font-weight: 600;
}

.form-meta {
  display: flex;
  gap: 15px;
  margin-bottom: 8px;
}

.field-count,
.row-count {
  color: #6b7280;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 5px;
}

.field-count i,
.row-count i {
  color: #3498db;
}

.form-time {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.form-time small {
  color: #9ca3af;
  font-size: 0.8rem;
}

.form-card-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}
</style>
