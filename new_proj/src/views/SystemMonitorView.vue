<template>
  <div class="form-designer">
    <!-- 页面头部 -->
    <div class="page-header">
      <button class="back-btn" @click="goBack">
        <i class="fas fa-arrow-left"></i>
        返回主页
      </button>
      <h1 class="page-title">自定义表单设计器</h1>
      <p class="page-subtitle">拖拽字段创建自定义表单</p>
    </div>

    <!-- 表单设计器组件 -->
    <FormDesigner
      ref="formDesigner"
      :initial-form="currentForm"
      @save-form="handleSaveForm"
      @form-change="handleFormChange"
      @field-add="handleFieldAdd"
    />

    <!-- 保存的表单列表 -->
    <div class="saved-forms" v-if="savedForms.length > 0">
      <h3>已保存的表单</h3>
      <div class="forms-grid">
        <div
          v-for="form in savedForms"
          :key="form.id"
          class="form-card"
          @click="loadForm(form)"
        >
          <div class="form-card-header">
            <h4>{{ form.name }}</h4>
            <span class="field-count">{{ form.fields.length }} 个字段</span>
          </div>
          <div class="form-card-actions">
            <a-button size="small" @click.stop="duplicateForm(form)">
              <i class="fas fa-copy"></i>
              复制
            </a-button>
            <a-button
              size="small"
              @click.stop="deleteForm(form.id)"
              type="danger"
            >
              <i class="fas fa-trash"></i>
              删除
            </a-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import FormDesigner from "@/components/FormDesigner.vue";

export default {
  name: "SystemMonitorView",
  components: {
    FormDesigner,
  },
  data() {
    return {
      // 当前正在设计的表单
      currentForm: {
        id: null,
        name: "",
        fields: [],
      },

      // 已保存的表单列表
      savedForms: [],
    };
  },
  mounted() {
    // 从localStorage加载已保存的表单
    this.loadSavedForms();
  },
  methods: {
    // 返回主页
    goBack() {
      this.$router.push("/");
    },

    // 处理表单保存
    handleSaveForm(formData) {
      // 检查是否是更新现有表单
      const existingIndex = this.savedForms.findIndex(
        (f) => f.id === formData.id
      );
      if (existingIndex >= 0) {
        this.savedForms[existingIndex] = formData;
        this.$message.success("表单已更新");
      } else {
        this.savedForms.push(formData);
        this.$message.success("表单已保存");
      }

      // 保存到localStorage
      this.saveForms();
    },

    // 处理表单变化
    handleFormChange(formData) {
      this.currentForm = formData;
    },

    // 处理字段添加
    handleFieldAdd(evt) {
      console.log("字段已添加:", evt);
    },

    // 从localStorage加载已保存的表单
    loadSavedForms() {
      try {
        const saved = localStorage.getItem("customForms");
        if (saved) {
          this.savedForms = JSON.parse(saved);
        }
      } catch (error) {
        console.error("加载表单失败:", error);
      }
    },

    // 保存表单到localStorage
    saveForms() {
      try {
        localStorage.setItem("customForms", JSON.stringify(this.savedForms));
      } catch (error) {
        console.error("保存表单失败:", error);
        this.$message.error("保存失败");
      }
    },

    // 加载表单
    loadForm(form) {
      this.currentForm = {
        ...form,
        fields: form.fields.map((field) => ({ ...field })), // 深拷贝字段
      };
      // 通知FormDesigner组件更新
      this.$refs.formDesigner.setFormData(this.currentForm);
      this.$message.success(`已加载表单: ${form.name}`);
    },

    // 复制表单
    duplicateForm(form) {
      const newForm = {
        ...form,
        id: Date.now(),
        name: form.name + " (副本)",
        createTime: new Date().toLocaleString(),
        updateTime: new Date().toLocaleString(),
        fields: form.fields.map((field) => ({ ...field })),
      };
      this.savedForms.push(newForm);
      this.saveForms();
      this.$message.success("表单已复制");
    },

    // 删除表单
    deleteForm(formId) {
      this.$confirm({
        title: "确认删除",
        content: "确定要删除这个表单吗？",
        onOk: () => {
          this.savedForms = this.savedForms.filter((f) => f.id !== formId);
          this.saveForms();
          this.$message.success("表单已删除");
        },
      });
    },
  },
};
</script>

<style scoped>
/* 主容器样式 */
.form-designer {
  padding: 20px;
  height: 100vh;
  background: #f5f6fa;
  overflow: hidden;
}

/* 页面头部样式 */
.page-header {
  margin-bottom: 20px;
}

.back-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  margin-bottom: 20px;
  transition: background 0.3s ease;
}

.back-btn:hover {
  background: #2980b9;
}

.back-btn i {
  margin-right: 8px;
}

.page-title {
  font-size: 2.5rem;
  color: #2c3e50;
  margin: 0 0 10px 0;
  font-weight: 600;
}

.page-subtitle {
  font-size: 1.2rem;
  color: #7f8c8d;
  margin: 0;
}

/* 保存的表单列表样式 */
.saved-forms {
  margin-top: 30px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.saved-forms h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 1.3rem;
}

.forms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.form-card {
  padding: 20px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  background: #f8f9fa;
  cursor: pointer;
  transition: all 0.3s ease;
}

.form-card:hover {
  border-color: #3498db;
  background: #ffffff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.2);
}

.form-card-header {
  margin-bottom: 15px;
}

.form-card-header h4 {
  margin: 0 0 5px 0;
  color: #2c3e50;
  font-size: 1.1rem;
}

.form-card-header .field-count {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.form-card-actions {
  display: flex;
  gap: 10px;
}

/* 保存的表单列表样式 */
.saved-forms {
  margin-top: 30px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.saved-forms h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 1.3rem;
}

.forms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.form-card {
  padding: 20px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  background: #f8f9fa;
  cursor: pointer;
  transition: all 0.3s ease;
}

.form-card:hover {
  border-color: #3498db;
  background: #ffffff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.2);
}

.form-card-header {
  margin-bottom: 15px;
}

.form-card-header h4 {
  margin: 0 0 5px 0;
  color: #2c3e50;
  font-size: 1.1rem;
}

.form-card-header .field-count {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.form-card-actions {
  display: flex;
  gap: 10px;
}
</style>
