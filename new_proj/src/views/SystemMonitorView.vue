<template>
  <div class="form-designer">
    <!-- 页面头部 -->
    <div class="page-header">
      <button class="back-btn" @click="goBack">
        <i class="fas fa-arrow-left"></i>
        返回主页
      </button>
      <h1 class="page-title">自定义表单设计器</h1>
      <p class="page-subtitle">拖拽字段创建自定义表单</p>
    </div>

    <div class="designer-container">
      <!-- 左侧字段库 -->
      <div class="field-library">
        <div class="library-header">
          <h3>
            <i class="fas fa-th-list"></i>
            字段库
          </h3>
        </div>

        <div class="field-categories">
          <!-- 基础字段 -->
          <div class="category">
            <h4>基础字段</h4>
            <draggable
              v-model="availableFields"
              :group="{ name: 'fields', pull: 'clone', put: false }"
              :clone="cloneField"
              :sort="false"
              class="field-list"
            >
              <div
                v-for="field in availableFields"
                :key="field.id"
                class="field-item"
                :class="field.type"
              >
                <i :class="field.icon"></i>
                <span>{{ field.label }}</span>
                <small>{{ field.description }}</small>
              </div>
            </draggable>
          </div>
        </div>
      </div>

      <!-- 中间表单设计区域 -->
      <div class="form-canvas">
        <div class="canvas-header">
          <div class="form-info">
            <a-input
              v-model="currentForm.name"
              placeholder="请输入表单名称"
              class="form-name-input"
            />
            <span class="field-count">{{ currentForm.fields.length }} 个字段</span>
          </div>
          <div class="canvas-actions">
            <a-button @click="previewForm" type="primary">
              <i class="fas fa-eye"></i>
              预览
            </a-button>
            <a-button @click="saveForm" type="primary">
              <i class="fas fa-save"></i>
              保存表单
            </a-button>
            <a-button @click="clearForm">
              <i class="fas fa-trash"></i>
              清空
            </a-button>
          </div>
        </div>

        <!-- 表单画布 -->
        <div class="canvas-area">
          <div v-if="currentForm.fields.length === 0" class="empty-canvas">
            <i class="fas fa-mouse-pointer"></i>
            <p>从左侧拖拽字段到这里开始设计表单</p>
          </div>

          <draggable
            v-model="currentForm.fields"
            group="fields"
            class="form-fields"
            @add="onFieldAdd"
          >
            <div
              v-for="(field, index) in currentForm.fields"
              :key="field.id"
              class="form-field"
              :class="{ active: selectedFieldIndex === index }"
              @click="selectField(index)"
            >
              <!-- 字段渲染 -->
              <div class="field-wrapper">
                <label class="field-label">
                  {{ field.label }}
                  <span v-if="field.required" class="required-mark">*</span>
                </label>

                <!-- 根据字段类型渲染不同的组件 -->
                <component
                  :is="getFieldComponent(field.type)"
                  v-bind="getFieldProps(field)"
                  :disabled="true"
                  class="field-component"
                />
              </div>

              <!-- 字段操作按钮 -->
              <div class="field-actions">
                <a-button size="small" @click.stop="moveFieldUp(index)" :disabled="index === 0">
                  <i class="fas fa-arrow-up"></i>
                </a-button>
                <a-button size="small" @click.stop="moveFieldDown(index)" :disabled="index === currentForm.fields.length - 1">
                  <i class="fas fa-arrow-down"></i>
                </a-button>
                <a-button size="small" @click.stop="duplicateField(index)">
                  <i class="fas fa-copy"></i>
                </a-button>
                <a-button size="small" @click.stop="removeField(index)" type="danger">
                  <i class="fas fa-trash"></i>
                </a-button>
              </div>
            </div>
          </draggable>
        </div>
      </div>

      <!-- 右侧属性配置面板 -->
      <div class="property-panel">
        <div class="panel-header">
          <h3>
            <i class="fas fa-cog"></i>
            字段属性
          </h3>
        </div>

        <div class="panel-content">
          <div v-if="selectedField" class="field-properties">
            <!-- 基础属性 -->
            <div class="property-group">
              <h4>基础属性</h4>

              <div class="property-item">
                <label>字段标签</label>
                <a-input v-model="selectedField.label" />
              </div>

              <div class="property-item">
                <label>字段名称</label>
                <a-input v-model="selectedField.name" />
              </div>

              <div class="property-item">
                <label>占位符</label>
                <a-input v-model="selectedField.placeholder" />
              </div>

              <div class="property-item">
                <label>是否必填</label>
                <a-switch v-model="selectedField.required" />
              </div>
            </div>

            <!-- 样式属性 -->
            <div class="property-group">
              <h4>样式属性</h4>

              <div class="property-item">
                <label>字段宽度</label>
                <a-select v-model="selectedField.width" style="width: 100%">
                  <a-select-option value="25%">25%</a-select-option>
                  <a-select-option value="33%">33%</a-select-option>
                  <a-select-option value="50%">50%</a-select-option>
                  <a-select-option value="75%">75%</a-select-option>
                  <a-select-option value="100%">100%</a-select-option>
                </a-select>
              </div>
            </div>
          </div>

          <div v-else class="no-selection">
            <i class="fas fa-hand-pointer"></i>
            <p>请选择一个字段进行配置</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import draggable from 'vuedraggable'

export default {
  name: "FormDesigner",
  components: {
    draggable
  },

<style scoped>
.system-monitor {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 40px;
}

.back-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  margin-bottom: 20px;
  transition: background 0.3s ease;
}

.back-btn:hover {
  background: #2980b9;
}

.back-btn i {
  margin-right: 8px;
}

.page-title {
  font-size: 2.5rem;
  color: #2c3e50;
  margin: 0 0 10px 0;
  font-weight: 600;
}

.page-subtitle {
  font-size: 1.2rem;
  color: #7f8c8d;
  margin: 0;
}

.content-area {
  background: #f8f9fa;
  border-radius: 15px;
  padding: 40px;
  min-height: 400px;
}

.placeholder-card {
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
}

.placeholder-icon {
  width: 100px;
  height: 100px;
  margin: 0 auto 30px;
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.placeholder-icon i {
  font-size: 3rem;
  color: white;
}

.placeholder-card h3 {
  font-size: 1.8rem;
  color: #2c3e50;
  margin-bottom: 20px;
}

.placeholder-card p {
  font-size: 1.1rem;
  color: #7f8c8d;
  margin-bottom: 20px;
  line-height: 1.6;
}

.feature-list {
  text-align: left;
  max-width: 300px;
  margin: 0 auto;
  padding: 0;
  list-style: none;
}

.feature-list li {
  padding: 8px 0;
  color: #34495e;
  position: relative;
  padding-left: 25px;
}

.feature-list li:before {
  content: "•";
  color: #e74c3c;
  font-weight: bold;
  position: absolute;
  left: 0;
}

@media (max-width: 768px) {
  .system-monitor {
    padding: 15px;
  }

  .page-title {
    font-size: 2rem;
  }

  .content-area {
    padding: 20px;
  }
}
</style>
