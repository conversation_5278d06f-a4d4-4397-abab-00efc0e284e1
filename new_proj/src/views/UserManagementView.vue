<template>
  <div class="user-management">
    <div class="page-header">
      <button class="back-btn" @click="goBack">
        <i class="fas fa-arrow-left"></i>
        返回主页
      </button>
      <h1 class="page-title">用户管理</h1>
      <p class="page-subtitle">管理系统用户和权限</p>
    </div>

    <div class="content-area">
      <div class="placeholder-card">
        <div class="placeholder-icon">
          <i class="fas fa-users"></i>
        </div>
        <h3>用户管理功能</h3>
        <p>此页面预留给用户管理功能，您可以在这里添加：</p>
        <ul class="feature-list">
          <li>用户账户管理</li>
          <li>权限分配设置</li>
          <li>角色管理配置</li>
          <li>用户活动监控</li>
          <li>密码策略管理</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "UserManagementView",
  methods: {
    goBack() {
      this.$router.push("/");
    },
  },
};
</script>

<style scoped>
.user-management {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 40px;
}

.back-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  margin-bottom: 20px;
  transition: background 0.3s ease;
}

.back-btn:hover {
  background: #2980b9;
}

.back-btn i {
  margin-right: 8px;
}

.page-title {
  font-size: 2.5rem;
  color: #2c3e50;
  margin: 0 0 10px 0;
  font-weight: 600;
}

.page-subtitle {
  font-size: 1.2rem;
  color: #7f8c8d;
  margin: 0;
}

.content-area {
  background: #f8f9fa;
  border-radius: 15px;
  padding: 40px;
  min-height: 400px;
}

.placeholder-card {
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
}

.placeholder-icon {
  width: 100px;
  height: 100px;
  margin: 0 auto 30px;
  background: linear-gradient(135deg, #1abc9c, #16a085);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.placeholder-icon i {
  font-size: 3rem;
  color: white;
}

.placeholder-card h3 {
  font-size: 1.8rem;
  color: #2c3e50;
  margin-bottom: 20px;
}

.placeholder-card p {
  font-size: 1.1rem;
  color: #7f8c8d;
  margin-bottom: 20px;
  line-height: 1.6;
}

.feature-list {
  text-align: left;
  max-width: 300px;
  margin: 0 auto;
  padding: 0;
  list-style: none;
}

.feature-list li {
  padding: 8px 0;
  color: #34495e;
  position: relative;
  padding-left: 25px;
}

.feature-list li:before {
  content: "•";
  color: #1abc9c;
  font-weight: bold;
  position: absolute;
  left: 0;
}

@media (max-width: 768px) {
  .user-management {
    padding: 15px;
  }

  .page-title {
    font-size: 2rem;
  }

  .content-area {
    padding: 20px;
  }
}
</style>
