<template>
  <div class="dark-topology-container">
    <!-- 顶部导航栏 -->
    <div class="top-navbar">
      <div class="navbar-left">
        <div class="logo-section">
          <a-icon type="deployment-unit" class="logo-icon" />
          <span class="logo-text">网络拓扑监控中心</span>
        </div>
        <div class="nav-tabs">
          <div
            class="nav-tab"
            :class="{ active: activeTab === 'topology' }"
            @click="switchTab('topology')"
          >
            <a-icon type="cluster" />
            <span>实时拓扑</span>
          </div>
          <div
            class="nav-tab"
            :class="{ active: activeTab === 'performance' }"
            @click="switchTab('performance')"
          >
            <a-icon type="line-chart" />
            <span>性能监控</span>
          </div>
          <div
            class="nav-tab"
            :class="{ active: activeTab === 'alerts' }"
            @click="switchTab('alerts')"
          >
            <a-icon type="alert" />
            <span>告警中心</span>
          </div>
        </div>
      </div>

      <div class="navbar-right">
        <div class="system-status">
          <div class="status-indicator online" />
          <span>系统正常</span>
        </div>
        <div class="time-display">{{ currentTime }}</div>
        <a-dropdown>
          <div class="user-menu">
            <a-avatar size="small" icon="user" />
            <span>运维管理员</span>
            <a-icon type="down" />
          </div>
          <a-menu slot="overlay" @click="handleUserMenuClick">
            <a-menu-item key="profile">
              <a-icon type="user" />
              个人设置
            </a-menu-item>
            <a-menu-item key="settings">
              <a-icon type="setting" />
              系统配置
            </a-menu-item>
            <a-menu-divider />
            <a-menu-item key="logout">
              <a-icon type="logout" />
              退出登录
            </a-menu-item>
          </a-menu>
        </a-dropdown>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧监控面板 -->
      <div class="monitoring-panel">
        <!-- 实时统计 -->
        <div class="stats-section">
          <h3>实时统计</h3>
          <div class="stats-grid">
            <div class="stat-item" v-for="stat in deviceStats" :key="stat.type">
              <div class="stat-icon" :class="stat.type">
                <a-icon :type="stat.icon" />
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ stat.count }}</div>
                <div class="stat-label">{{ stat.label }}</div>
                <div class="stat-trend" :class="stat.trend">
                  <a-icon
                    :type="stat.trend === 'up' ? 'arrow-up' : 'arrow-down'"
                  />
                  {{ stat.change }}%
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 告警列表 -->
        <div class="alerts-section">
          <div class="alerts-header">
            <h3>
              <a-icon type="warning" />
              活跃告警
              <a-badge :count="alerts.length" :offset="[10, 0]" />
            </h3>
            <div class="alerts-actions">
              <a-button
                type="link"
                size="small"
                @click="exportAlerts"
                title="导出告警"
              >
                <a-icon type="download" />
              </a-button>
              <a-button
                type="link"
                size="small"
                @click="clearAllAlerts"
                title="清除所有"
              >
                <a-icon type="delete" />
              </a-button>
            </div>
          </div>
          <div class="alerts-list">
            <div
              v-for="alert in alerts.slice(0, 5)"
              :key="alert.id"
              class="alert-item"
              :class="[alert.level, { handled: alert.status === 'handled' }]"
            >
              <div class="alert-indicator" />
              <div class="alert-content">
                <div class="alert-title">{{ alert.title }}</div>
                <div class="alert-device">{{ alert.device }}</div>
                <div class="alert-time">{{ alert.time }}</div>
                <div v-if="alert.status === 'handled'" class="alert-handled">
                  已处理 - {{ alert.handledTime }}
                </div>
              </div>
              <div class="alert-actions">
                <a-button
                  v-if="alert.status !== 'handled'"
                  type="link"
                  size="small"
                  @click="handleAlert(alert)"
                >
                  处理
                </a-button>
                <span v-else class="handled-badge">已处理</span>
              </div>
            </div>
            <div v-if="alerts.length === 0" class="no-alerts">
              <a-icon type="check-circle" />
              <p>暂无告警</p>
            </div>
          </div>
        </div>

        <!-- 性能监控 -->
        <div class="performance-section">
          <h3>
            <a-icon type="dashboard" />
            系统性能
          </h3>
          <div class="performance-metrics">
            <div
              class="metric-item"
              v-for="metric in performanceMetrics"
              :key="metric.name"
            >
              <div class="metric-header">
                <span class="metric-name">{{ metric.name }}</span>
                <span class="metric-value">{{ metric.value }}%</span>
              </div>
              <div class="metric-bar">
                <div
                  class="metric-fill"
                  :style="{ width: metric.value + '%' }"
                  :class="getMetricClass(metric.value)"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 中央内容区域 -->
      <div class="main-content-area">
        <!-- 实时拓扑标签页 -->
        <div v-show="activeTab === 'topology'" class="topology-main">
          <!-- 控制工具栏 -->
          <div class="control-toolbar">
            <div class="toolbar-left">
              <div class="search-box">
                <a-input-search
                  v-model="searchKeyword"
                  placeholder="搜索设备..."
                  @search="handleSearch"
                  @change="handleSearchChange"
                />
              </div>
              <div class="filter-controls">
                <a-select
                  v-model="selectedType"
                  placeholder="设备类型"
                  allowClear
                  @change="handleTypeFilter"
                  style="width: 120px"
                >
                  <a-select-option value="server">服务器</a-select-option>
                  <a-select-option value="switch">交换机</a-select-option>
                  <a-select-option value="router">路由器</a-select-option>
                  <a-select-option value="firewall">防火墙</a-select-option>
                  <a-select-option value="storage">存储</a-select-option>
                </a-select>
                <a-select
                  v-model="selectedStatus"
                  placeholder="设备状态"
                  allowClear
                  @change="handleStatusFilter"
                  style="width: 120px"
                >
                  <a-select-option value="online">在线</a-select-option>
                  <a-select-option value="offline">离线</a-select-option>
                  <a-select-option value="warning">告警</a-select-option>
                  <a-select-option value="error">故障</a-select-option>
                </a-select>
              </div>
            </div>

            <div class="toolbar-right">
              <div class="layout-switcher">
                <a-button-group>
                  <a-button
                    v-for="layout in layoutOptions"
                    :key="layout.value"
                    :type="layoutType === layout.value ? 'primary' : 'ghost'"
                    @click="changeLayout(layout.value)"
                    :title="layout.label"
                  >
                    <a-icon :type="layout.icon" />
                  </a-button>
                </a-button-group>
              </div>
              <div class="view-controls">
                <a-button-group>
                  <a-button @click="zoomIn" title="放大">
                    <a-icon type="zoom-in" />
                  </a-button>
                  <a-button @click="zoomOut" title="缩小">
                    <a-icon type="zoom-out" />
                  </a-button>
                  <a-button @click="resetView" title="重置">
                    <a-icon type="border" />
                  </a-button>
                  <a-button @click="fullscreen" title="全屏">
                    <a-icon type="fullscreen" />
                  </a-button>
                </a-button-group>
              </div>
              <div class="action-controls">
                <a-button @click="refreshData" :loading="loading" title="刷新">
                  <a-icon type="reload" />
                </a-button>
                <a-button @click="exportData" title="导出">
                  <a-icon type="download" />
                </a-button>
                <a-button @click="showTopologySettings" title="拓扑设置">
                  <a-icon type="setting" />
                </a-button>
              </div>
            </div>
          </div>

          <!-- 拓扑图画布 -->
          <div class="topology-canvas">
            <a-spin :spinning="loading" tip="正在加载网络拓扑..." size="large">
              <div
                id="topology-chart-v3"
                class="chart-container"
                :style="{ height: chartHeight + 'px' }"
              />
            </a-spin>
          </div>

          <!-- 迷你地图 -->
          <div class="minimap" v-if="showMinimap">
            <div class="minimap-header">
              <span>导航地图</span>
              <div class="minimap-controls">
                <a-button
                  type="text"
                  size="small"
                  @click="toggleMinimapMode"
                  title="切换模式"
                >
                  <a-icon
                    :type="minimapMode === 'overview' ? 'eye' : 'radar-chart'"
                  />
                </a-button>
                <a-button
                  type="text"
                  size="small"
                  @click="showMinimap = false"
                  title="关闭"
                >
                  <a-icon type="close" />
                </a-button>
              </div>
            </div>
            <div class="minimap-content">
              <!-- 概览模式 -->
              <div v-if="minimapMode === 'overview'" class="minimap-overview">
                <div class="overview-stats">
                  <div class="stat-item">
                    <span class="stat-label">总设备</span>
                    <span class="stat-value">{{ devices.length }}</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">在线</span>
                    <span class="stat-value online">{{
                      getDeviceCountByStatus("online")
                    }}</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">告警</span>
                    <span class="stat-value warning">{{
                      getDeviceCountByStatus("warning")
                    }}</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">故障</span>
                    <span class="stat-value error">{{
                      getDeviceCountByStatus("error")
                    }}</span>
                  </div>
                </div>
              </div>

              <!-- 拓扑预览模式 -->
              <div v-else class="minimap-topology">
                <div
                  v-for="device in filteredDevices.slice(0, 20)"
                  :key="device.id"
                  class="mini-device"
                  :class="[device.type, device.status]"
                  :title="device.name"
                  @click="selectDeviceFromMinimap(device)"
                >
                  <div class="mini-device-dot"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- 选择工具栏 -->
          <div class="selection-toolbar" v-if="selectedDevices.length > 0">
            <div class="selection-info">
              已选择 {{ selectedDevices.length }} 个设备
            </div>
            <div class="selection-actions">
              <a-button size="small" @click="batchRestart">
                <a-icon type="reload" />
                批量重启
              </a-button>
              <a-button size="small" @click="batchShutdown">
                <a-icon type="poweroff" />
                批量关机
              </a-button>
              <a-button size="small" @click="batchMonitor">
                <a-icon type="eye" />
                批量监控
              </a-button>
              <a-button size="small" @click="clearSelection">
                <a-icon type="close" />
                取消选择
              </a-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧详情面板 -->
      <div class="details-panel" :class="{ collapsed: detailsPanelCollapsed }">
        <div class="panel-header">
          <span v-if="!detailsPanelCollapsed">设备详情</span>
          <a-button
            type="text"
            size="small"
            @click="detailsPanelCollapsed = !detailsPanelCollapsed"
          >
            <a-icon
              :type="detailsPanelCollapsed ? 'menu-unfold' : 'menu-fold'"
            />
          </a-button>
        </div>

        <div class="panel-content" v-if="!detailsPanelCollapsed">
          <div v-if="selectedDevice" class="device-details">
            <!-- 设备详情内容 -->
            <div class="device-header">
              <div class="device-icon" :class="selectedDevice.type">
                <a-icon :type="getDeviceIcon(selectedDevice.type)" />
              </div>
              <div class="device-info">
                <h4>{{ selectedDevice.name }}</h4>
                <p>{{ getDeviceTypeLabel(selectedDevice.type) }}</p>
              </div>
              <div class="device-status" :class="selectedDevice.status">
                {{ getStatusLabel(selectedDevice.status) }}
              </div>
            </div>

            <!-- 基本信息 -->
            <div class="info-section">
              <h5>基本信息</h5>
              <div class="info-grid">
                <div class="info-item">
                  <label>IP地址</label>
                  <span>{{ selectedDevice.ip }}</span>
                </div>
                <div class="info-item">
                  <label>MAC地址</label>
                  <span>{{ selectedDevice.mac }}</span>
                </div>
                <div class="info-item">
                  <label>位置</label>
                  <span>{{ selectedDevice.location }}</span>
                </div>
                <div class="info-item">
                  <label>运行时间</label>
                  <span>{{ selectedDevice.uptime }}</span>
                </div>
              </div>
            </div>

            <!-- 性能指标 -->
            <div class="metrics-section" v-if="selectedDevice.metrics">
              <h5>性能指标</h5>
              <div class="metrics-grid">
                <div
                  class="metric-card"
                  v-for="(value, key) in selectedDevice.metrics"
                  :key="key"
                >
                  <div class="metric-header">
                    <span>{{ getMetricLabel(key) }}</span>
                    <span class="metric-value">{{ value }}%</span>
                  </div>
                  <div class="metric-progress">
                    <div
                      class="progress-bar"
                      :style="{ width: value + '%' }"
                      :class="getMetricClass(value)"
                    />
                  </div>
                </div>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="actions-section">
              <h5>设备操作</h5>
              <div class="action-buttons">
                <a-button block @click="restartDevice">
                  <a-icon type="reload" />
                  重启设备
                </a-button>
                <a-button block @click="shutdownDevice">
                  <a-icon type="poweroff" />
                  关闭设备
                </a-button>
                <a-button block @click="monitorDevice">
                  <a-icon type="eye" />
                  实时监控
                </a-button>
                <a-button block @click="configDevice">
                  <a-icon type="setting" />
                  设备配置
                </a-button>
              </div>
            </div>
          </div>

          <div v-else class="no-selection">
            <a-icon type="desktop" />
            <p>点击设备查看详细信息</p>
          </div>
        </div>

        <!-- 性能监控标签页 -->
        <div v-show="activeTab === 'performance'" class="performance-main">
          <div class="performance-header">
            <h2>
              <a-icon type="line-chart" />
              系统性能监控
            </h2>
            <p>实时监控系统各项性能指标</p>
          </div>

          <div class="performance-grid">
            <div
              v-for="metric in performanceMetrics"
              :key="metric.name"
              class="performance-card"
              :class="{ warning: metric.value > metric.threshold }"
            >
              <div class="metric-header">
                <span class="metric-name">{{ metric.name }}</span>
                <span class="metric-value"
                  >{{ metric.value }}{{ metric.unit }}</span
                >
              </div>
              <div class="metric-progress">
                <a-progress
                  :percent="metric.value"
                  :status="
                    metric.value > metric.threshold ? 'exception' : 'normal'
                  "
                  :stroke-color="
                    metric.value > metric.threshold ? '#ff4d4f' : '#1890ff'
                  "
                />
              </div>
              <div class="metric-threshold">
                阈值: {{ metric.threshold }}{{ metric.unit }}
              </div>
            </div>
          </div>
        </div>

        <!-- 告警中心标签页 -->
        <div v-show="activeTab === 'alerts'" class="alerts-main">
          <div class="alerts-header-main">
            <h2>
              <a-icon type="alert" />
              告警管理中心
            </h2>
            <div class="alerts-summary">
              <div class="summary-item critical">
                <span class="count">{{
                  alerts.filter((a) => a.level === "critical").length
                }}</span>
                <span class="label">严重告警</span>
              </div>
              <div class="summary-item warning">
                <span class="count">{{
                  alerts.filter((a) => a.level === "warning").length
                }}</span>
                <span class="label">警告告警</span>
              </div>
              <div class="summary-item info">
                <span class="count">{{
                  alerts.filter((a) => a.level === "info").length
                }}</span>
                <span class="label">信息告警</span>
              </div>
            </div>
          </div>

          <div class="alerts-table">
            <a-table
              :columns="alertColumns"
              :data-source="alerts"
              :pagination="{ pageSize: 10 }"
              row-key="id"
            >
              <template slot="level" slot-scope="level">
                <a-tag :color="getAlertLevelColor(level)">
                  {{ getAlertLevelText(level) }}
                </a-tag>
              </template>
              <template slot="action" slot-scope="text, record">
                <a-button
                  v-if="record.status !== 'handled'"
                  type="link"
                  @click="handleAlert(record)"
                >
                  处理
                </a-button>
                <span v-else class="handled-text">已处理</span>
              </template>
            </a-table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "DarkDeviceTopology",
  data() {
    return {
      // 基础状态
      loading: false,
      currentTime: "",
      timeInterval: null,

      // UI控制
      activeTab: "topology", // 当前激活的标签页
      detailsPanelCollapsed: false,
      showMinimap: true,
      minimapMode: "overview", // 迷你地图模式：overview 或 topology
      showSettings: false,
      chartHeight: 600,

      // 搜索和筛选
      searchKeyword: "",
      selectedStatus: undefined,
      selectedType: undefined,

      // 布局和显示
      layoutType: "force",

      // 选择的设备
      selectedDevice: null,
      selectedDevices: [],

      // 拓扑图数据
      topologyChart: null,
      devices: [],
      connections: [],
      filteredDevices: [],
      filteredConnections: [],

      // 统计数据
      deviceStats: [
        {
          type: "online",
          label: "在线设备",
          count: 0,
          icon: "check-circle",
          trend: "up",
          change: 2.5,
        },
        {
          type: "offline",
          label: "离线设备",
          count: 0,
          icon: "close-circle",
          trend: "down",
          change: 1.2,
        },
        {
          type: "warning",
          label: "告警设备",
          count: 0,
          icon: "exclamation-circle",
          trend: "up",
          change: 0.8,
        },
        {
          type: "error",
          label: "故障设备",
          count: 0,
          icon: "warning",
          trend: "down",
          change: 0.3,
        },
      ],

      // 告警数据
      alerts: [
        {
          id: 1,
          level: "critical",
          title: "CPU使用率过高",
          device: "SRV-01",
          time: "2分钟前",
        },
        {
          id: 2,
          level: "warning",
          title: "内存使用率告警",
          device: "SRV-03",
          time: "5分钟前",
        },
        {
          id: 3,
          level: "critical",
          title: "网络连接中断",
          device: "SW-02",
          time: "8分钟前",
        },
        {
          id: 4,
          level: "info",
          title: "设备重启完成",
          device: "RTR-01",
          time: "15分钟前",
        },
        {
          id: 5,
          level: "warning",
          title: "磁盘空间不足",
          device: "STO-01",
          time: "20分钟前",
        },
      ],

      // 性能指标
      performanceMetrics: [
        { name: "CPU使用率", value: 65, unit: "%", threshold: 80 },
        { name: "内存使用率", value: 78, unit: "%", threshold: 85 },
        { name: "网络带宽", value: 45, unit: "%", threshold: 90 },
        { name: "磁盘I/O", value: 32, unit: "%", threshold: 75 },
      ],

      // 性能监控更新间隔
      performanceInterval: null,

      // 告警表格列定义
      alertColumns: [
        {
          title: "时间",
          dataIndex: "time",
          key: "time",
          width: 120,
        },
        {
          title: "级别",
          dataIndex: "level",
          key: "level",
          width: 80,
          scopedSlots: { customRender: "level" },
        },
        {
          title: "告警标题",
          dataIndex: "title",
          key: "title",
        },
        {
          title: "设备",
          dataIndex: "device",
          key: "device",
          width: 120,
        },
        {
          title: "操作",
          key: "action",
          width: 100,
          scopedSlots: { customRender: "action" },
        },
      ],

      // 布局选项
      layoutOptions: [
        { value: "force", label: "力导向图", icon: "cluster" },
        { value: "circular", label: "环形布局", icon: "radius-setting" },
        { value: "grid", label: "网格布局", icon: "appstore" },
        { value: "hierarchy", label: "分层布局", icon: "partition" },
      ],

      // 设备类型配置
      deviceTypes: [
        {
          value: "server",
          label: "服务器",
          color: "#00d4ff",
          icon: "database",
        },
        { value: "switch", label: "交换机", color: "#00ff88", icon: "gateway" },
        { value: "router", label: "路由器", color: "#ff6b00", icon: "wifi" },
        {
          value: "firewall",
          label: "防火墙",
          color: "#ff0040",
          icon: "safety",
        },
        { value: "storage", label: "存储设备", color: "#8000ff", icon: "hdd" },
      ],

      // 设备状态配置
      deviceStatuses: [
        { value: "online", label: "在线", color: "#00ff88" },
        { value: "offline", label: "离线", color: "#666666" },
        { value: "warning", label: "告警", color: "#ffaa00" },
        { value: "error", label: "故障", color: "#ff0040" },
      ],
    };
  },
  mounted() {
    this.initTopology();
    this.loadDeviceData();
    this.calculateChartHeight();
    this.startTimeUpdate();
    this.startPerformanceMonitoring();
    window.addEventListener("resize", this.handleResize);
  },
  beforeDestroy() {
    if (this.topologyChart) {
      this.topologyChart.dispose();
    }
    if (this.timeInterval) {
      clearInterval(this.timeInterval);
    }
    if (this.performanceInterval) {
      clearInterval(this.performanceInterval);
    }
    window.removeEventListener("resize", this.handleResize);
  },
  methods: {
    // 初始化拓扑图
    initTopology() {
      const chartDom = document.getElementById("topology-chart-v3");
      if (chartDom) {
        this.topologyChart = echarts.init(chartDom, "white");
        this.setupChartEvents();
      }
    },

    // 开始时间更新
    startTimeUpdate() {
      this.updateTime();
      this.timeInterval = setInterval(this.updateTime, 1000);
    },

    // 更新时间显示
    updateTime() {
      const now = new Date();
      this.currentTime = now.toLocaleString("zh-CN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
      });
    },

    // 开始性能监控
    startPerformanceMonitoring() {
      this.updatePerformanceMetrics();
      this.performanceInterval = setInterval(
        this.updatePerformanceMetrics,
        5000
      );
    },

    // 更新性能指标
    updatePerformanceMetrics() {
      this.performanceMetrics.forEach((metric) => {
        // 模拟实时数据变化
        const change = (Math.random() - 0.5) * 10;
        let newValue = metric.value + change;
        newValue = Math.max(0, Math.min(100, newValue));
        metric.value = Math.round(newValue);

        // 检查是否超过阈值，生成告警
        if (metric.value > metric.threshold) {
          this.generatePerformanceAlert(metric);
        }
      });
    },

    // 生成性能告警
    generatePerformanceAlert(metric) {
      const existingAlert = this.alerts.find(
        (alert) =>
          alert.title.includes(metric.name) && alert.device === "系统监控"
      );

      if (!existingAlert) {
        const newAlert = {
          id: Date.now(),
          level: metric.value > metric.threshold + 10 ? "critical" : "warning",
          title: `${metric.name}超过阈值`,
          device: "系统监控",
          time: "刚刚",
          metric: metric.name,
          value: metric.value,
          threshold: metric.threshold,
        };

        this.alerts.unshift(newAlert);

        // 限制告警数量
        if (this.alerts.length > 20) {
          this.alerts = this.alerts.slice(0, 20);
        }

        // 显示通知
        this.$notification[newAlert.level]({
          message: "性能告警",
          description: `${metric.name}当前值${metric.value}%，超过阈值${metric.threshold}%`,
          duration: 4.5,
        });
      }
    },

    // 计算图表高度
    calculateChartHeight() {
      const windowHeight = window.innerHeight;
      this.chartHeight = Math.max(400, windowHeight - 250);
    },

    // 窗口大小变化处理
    handleResize() {
      this.calculateChartHeight();
      if (this.topologyChart) {
        this.topologyChart.resize();
      }
    },

    // 设置图表事件
    setupChartEvents() {
      if (this.topologyChart) {
        this.topologyChart.on("click", (params) => {
          if (params.dataType === "node") {
            this.handleDeviceClick(params.data);
          }
        });
      }
    },

    // 设备点击处理
    handleDeviceClick(nodeData) {
      this.selectedDevice = nodeData.value;

      // 多选逻辑
      const deviceIndex = this.selectedDevices.findIndex(
        (d) => d.id === nodeData.value.id
      );
      if (deviceIndex > -1) {
        this.selectedDevices.splice(deviceIndex, 1);
      } else {
        this.selectedDevices.push(nodeData.value);
      }

      this.renderTopology();
    },

    // 加载设备数据
    loadDeviceData() {
      this.loading = true;
      setTimeout(() => {
        this.devices = this.generateMockDevices();
        this.connections = this.generateMockConnections();
        this.updateDeviceStats();
        this.filterAndRenderTopology();
        this.loading = false;
      }, 1000);
    },

    // 生成模拟设备数据
    generateMockDevices() {
      const devices = [];
      const deviceConfigs = [
        { type: "server", count: 15, prefix: "SRV" },
        { type: "switch", count: 8, prefix: "SW" },
        { type: "router", count: 4, prefix: "RTR" },
        { type: "firewall", count: 3, prefix: "FW" },
        { type: "storage", count: 6, prefix: "STO" },
      ];

      let id = 1;
      deviceConfigs.forEach((config) => {
        for (let i = 1; i <= config.count; i++) {
          const status = this.getRandomStatus();
          devices.push({
            id: id++,
            name: `${config.prefix}-${i.toString().padStart(2, "0")}`,
            type: config.type,
            status: status,
            ip: `192.168.${Math.floor(Math.random() * 255)}.${Math.floor(
              Math.random() * 255
            )}`,
            mac: this.generateMacAddress(),
            location: `数据中心${Math.floor(Math.random() * 3) + 1}-机架${
              Math.floor(Math.random() * 50) + 1
            }`,
            lastUpdate: new Date().toLocaleString(),
            uptime: `${Math.floor(Math.random() * 365)}天${Math.floor(
              Math.random() * 24
            )}小时`,
            metrics:
              status === "online"
                ? {
                    cpu: Math.floor(Math.random() * 100),
                    memory: Math.floor(Math.random() * 100),
                    disk: Math.floor(Math.random() * 100),
                    network: Math.floor(Math.random() * 100),
                  }
                : null,
          });
        }
      });

      return devices;
    },

    // 生成模拟连接数据
    generateMockConnections() {
      // 简化连接生成逻辑
      const connections = [];
      const servers = this.devices.filter((d) => d.type === "server");
      const switches = this.devices.filter((d) => d.type === "switch");
      // const routers = this.devices.filter(d => d.type === 'router')
      // const firewalls = this.devices.filter(d => d.type === 'firewall')

      // 创建基本网络拓扑
      servers.forEach((server) => {
        const targetSwitch =
          switches[Math.floor(Math.random() * switches.length)];
        if (targetSwitch) {
          connections.push({
            source: server.id,
            target: targetSwitch.id,
            type: "ethernet",
            status: "active",
          });
        }
      });

      return connections;
    },

    // 获取随机状态
    getRandomStatus() {
      const statuses = ["online", "offline", "warning", "error"];
      const weights = [0.8, 0.05, 0.1, 0.05];
      const random = Math.random();
      let sum = 0;

      for (let i = 0; i < weights.length; i++) {
        sum += weights[i];
        if (random <= sum) {
          return statuses[i];
        }
      }
      return "online";
    },

    // 生成MAC地址
    generateMacAddress() {
      const chars = "0123456789ABCDEF";
      let mac = "";
      for (let i = 0; i < 6; i++) {
        if (i > 0) mac += ":";
        mac += chars[Math.floor(Math.random() * 16)];
        mac += chars[Math.floor(Math.random() * 16)];
      }
      return mac;
    },

    // 更新设备统计
    updateDeviceStats() {
      const counts = { online: 0, offline: 0, warning: 0, error: 0 };
      this.devices.forEach((device) => {
        counts[device.status]++;
      });

      this.deviceStats.forEach((stat) => {
        stat.count = counts[stat.type];
      });
    },

    // 筛选和渲染拓扑图
    filterAndRenderTopology() {
      this.filteredDevices = this.devices.filter((device) => {
        const matchesSearch =
          !this.searchKeyword ||
          device.name
            .toLowerCase()
            .includes(this.searchKeyword.toLowerCase()) ||
          device.ip.includes(this.searchKeyword);
        const matchesStatus =
          !this.selectedStatus || device.status === this.selectedStatus;
        const matchesType =
          !this.selectedType || device.type === this.selectedType;

        return matchesSearch && matchesStatus && matchesType;
      });

      const deviceIds = this.filteredDevices.map((d) => d.id);
      this.filteredConnections = this.connections.filter(
        (conn) =>
          deviceIds.includes(conn.source) && deviceIds.includes(conn.target)
      );

      this.renderTopology();
    },

    // 渲染拓扑图
    renderTopology() {
      if (!this.topologyChart) return;

      const nodes = this.filteredDevices.map((device) => ({
        id: device.id,
        name: device.name,
        category: device.type,
        value: device,
        symbolSize: this.getNodeSize(device.type),
        itemStyle: {
          color: this.getNodeColor(device),
          borderColor: this.selectedDevices.find((d) => d.id === device.id)
            ? "#00ff88"
            : "transparent",
          borderWidth: this.selectedDevices.find((d) => d.id === device.id)
            ? 3
            : 0,
          shadowColor: this.getNodeColor(device),
          shadowBlur: device.status === "online" ? 10 : 0,
        },
        label: {
          show: true,
          position: "bottom",
          fontSize: 10,
          color: "#ffffff",
          fontWeight: "bold",
        },
      }));

      const links = this.filteredConnections.map((conn) => ({
        source: conn.source,
        target: conn.target,
        lineStyle: {
          color: conn.status === "active" ? "#00d4ff" : "#666666",
          width: 2,
          opacity: 0.8,
        },
      }));

      // 根据布局类型设置不同的配置
      let layoutConfig = {};
      switch (this.layoutType) {
        case "force":
          layoutConfig = {
            layout: "force",
            force: {
              repulsion: 1000,
              gravity: 0.1,
              edgeLength: 200,
              layoutAnimation: true,
            },
          };
          break;
        case "circular":
          layoutConfig = {
            layout: "circular",
            circular: {
              rotateLabel: true,
            },
          };
          break;
        case "grid":
          layoutConfig = {
            layout: "none",
            // 手动设置网格位置
            data: nodes.map((node, index) => {
              const cols = Math.ceil(Math.sqrt(nodes.length));
              const row = Math.floor(index / cols);
              const col = index % cols;
              return {
                ...node,
                x: col * 150 + 100,
                y: row * 150 + 100,
                fixed: true,
              };
            }),
          };
          break;
        case "hierarchy":
          layoutConfig = {
            layout: "none",
            // 手动设置分层位置
            data: nodes.map((node) => {
              const level =
                node.value.type === "router"
                  ? 0
                  : node.value.type === "switch"
                  ? 1
                  : node.value.type === "firewall"
                  ? 2
                  : 3;
              const sameTypeNodes = nodes.filter(
                (n) =>
                  (n.value.type === "router"
                    ? 0
                    : n.value.type === "switch"
                    ? 1
                    : n.value.type === "firewall"
                    ? 2
                    : 3) === level
              );
              const posInLevel = sameTypeNodes.findIndex(
                (n) => n.id === node.id
              );
              return {
                ...node,
                x: posInLevel * 200 + 100,
                y: level * 150 + 100,
                fixed: true,
              };
            }),
          };
          break;
        default:
          layoutConfig = {
            layout: "force",
            force: {
              repulsion: 1000,
              gravity: 0.1,
              edgeLength: 200,
            },
          };
      }

      const option = {
        backgroundColor: "transparent",
        tooltip: {
          trigger: "item",
          backgroundColor: "rgba(0, 0, 0, 0.8)",
          borderColor: "#00d4ff",
          borderWidth: 1,
          textStyle: {
            color: "#ffffff",
          },
          formatter: (params) => {
            if (params.dataType === "node") {
              const device = params.data.value;
              return `
                <div style="padding: 8px;">
                  <div style="font-weight: bold; margin-bottom: 4px;">${
                    device.name
                  }</div>
                  <div>类型: ${this.getDeviceTypeLabel(device.type)}</div>
                  <div>状态: ${this.getStatusLabel(device.status)}</div>
                  <div>IP: ${device.ip}</div>
                </div>
              `;
            }
            return params.name;
          },
        },
        series: [
          {
            type: "graph",
            ...layoutConfig,
            data: layoutConfig.data || nodes,
            links: links,
            roam: true,
            focusNodeAdjacency: true,
            lineStyle: {
              opacity: 0.9,
              width: 2,
              curveness: 0.3,
            },
            emphasis: {
              focus: "adjacency",
              lineStyle: {
                width: 4,
              },
            },
          },
        ],
      };

      this.topologyChart.setOption(option, true);
    },

    // 获取节点大小
    getNodeSize(type) {
      const sizes = {
        server: 40,
        switch: 35,
        router: 45,
        firewall: 50,
        storage: 38,
      };
      return sizes[type] || 35;
    },

    // 获取节点颜色
    getNodeColor(device) {
      const deviceType = this.deviceTypes.find((t) => t.value === device.type);
      const typeColor = deviceType ? deviceType.color : "#00d4ff";
      const statusColors = {
        online: typeColor,
        offline: "#666666",
        warning: "#ffaa00",
        error: "#ff0040",
      };
      return statusColors[device.status] || typeColor;
    },

    // 获取设备图标
    getDeviceIcon(type) {
      const deviceType = this.deviceTypes.find((t) => t.value === type);
      return deviceType ? deviceType.icon : "desktop";
    },

    // 获取设备类型标签
    getDeviceTypeLabel(type) {
      const deviceType = this.deviceTypes.find((t) => t.value === type);
      return deviceType ? deviceType.label : type;
    },

    // 获取状态标签
    getStatusLabel(status) {
      const deviceStatus = this.deviceStatuses.find((s) => s.value === status);
      return deviceStatus ? deviceStatus.label : status;
    },

    // 获取指标类别
    getMetricClass(value) {
      if (value >= 80) return "critical";
      if (value >= 60) return "warning";
      return "normal";
    },

    // 获取指标标签
    getMetricLabel(key) {
      const labels = {
        cpu: "CPU",
        memory: "内存",
        disk: "磁盘",
        network: "网络",
      };
      return labels[key] || key;
    },

    // 标签页切换
    switchTab(tab) {
      this.activeTab = tab;
      this.$message.success(`已切换到${this.getTabLabel(tab)}`);

      // 根据不同标签页执行不同操作
      switch (tab) {
        case "topology":
          // 显示拓扑图相关内容
          this.renderTopology();
          break;
        case "performance":
          // 聚焦到性能监控
          this.scrollToPerformance();
          break;
        case "alerts":
          // 聚焦到告警中心
          this.scrollToAlerts();
          break;
      }
    },

    getTabLabel(tab) {
      const tabMap = {
        topology: "实时拓扑",
        performance: "性能监控",
        alerts: "告警中心",
      };
      return tabMap[tab] || tab;
    },

    // 滚动到性能监控区域
    scrollToPerformance() {
      const element = this.$el.querySelector(".performance-section");
      if (element) {
        element.scrollIntoView({ behavior: "smooth", block: "start" });
      }
    },

    // 滚动到告警中心区域
    scrollToAlerts() {
      const element = this.$el.querySelector(".alerts-section");
      if (element) {
        element.scrollIntoView({ behavior: "smooth", block: "start" });
      }
    },

    // 切换迷你地图模式
    toggleMinimapMode() {
      this.minimapMode =
        this.minimapMode === "overview" ? "topology" : "overview";
      this.$message.success(
        `已切换到${this.minimapMode === "overview" ? "概览" : "拓扑"}模式`
      );
    },

    // 根据状态获取设备数量
    getDeviceCountByStatus(status) {
      return this.devices.filter((device) => device.status === status).length;
    },

    // 从迷你地图选择设备
    selectDeviceFromMinimap(device) {
      this.selectedDevice = device;
      this.selectedDevices = [device];
      this.renderTopology();
      this.$message.success(`已选择设备: ${device.name}`);
    },

    // 获取告警级别颜色
    getAlertLevelColor(level) {
      const colorMap = {
        critical: "red",
        warning: "orange",
        info: "blue",
      };
      return colorMap[level] || "default";
    },

    // 获取告警级别文本
    getAlertLevelText(level) {
      const textMap = {
        critical: "严重",
        warning: "警告",
        info: "信息",
      };
      return textMap[level] || level;
    },

    // 用户菜单点击处理
    handleUserMenuClick({ key }) {
      switch (key) {
        case "profile":
          this.$message.info("个人设置功能开发中...");
          break;
        case "settings":
          this.showSystemSettings();
          break;
        case "logout":
          this.handleLogout();
          break;
      }
    },

    // 显示系统设置
    showSystemSettings() {
      this.$modal.info({
        title: "系统配置",
        content: "系统配置功能开发中，敬请期待！",
        okText: "确定",
      });
    },

    // 处理退出登录
    handleLogout() {
      this.$confirm({
        title: "退出登录",
        content: "确定要退出登录吗？",
        okText: "确定",
        cancelText: "取消",
        onOk: () => {
          this.$message.success("已退出登录");
          // 这里可以添加实际的退出登录逻辑
          // 比如清除token，跳转到登录页等
          setTimeout(() => {
            this.$router.push("/");
          }, 1000);
        },
      });
    },

    // 显示拓扑设置
    showTopologySettings() {
      this.$modal.info({
        title: "拓扑图设置",
        width: 600,
        content: `
          <div style="padding: 16px;">
            <h4>当前设置：</h4>
            <p>• 布局模式：${this.getLayoutLabel(this.layoutType)}</p>
            <p>• 显示设备数量：${this.filteredDevices.length}</p>
            <p>• 连接数量：${this.filteredConnections.length}</p>
            <p>• 迷你地图：${this.showMinimap ? "开启" : "关闭"}</p>
            <br>
            <h4>可用操作：</h4>
            <p>• 使用布局切换按钮改变拓扑布局</p>
            <p>• 使用筛选器过滤设备类型和状态</p>
            <p>• 使用搜索框查找特定设备</p>
            <p>• 点击设备查看详细信息</p>
          </div>
        `,
        okText: "确定",
      });
    },

    // 事件处理方法
    handleSearch(value) {
      this.searchKeyword = value;
      this.filterAndRenderTopology();
    },

    handleSearchChange(e) {
      this.searchKeyword = e.target.value;
      this.filterAndRenderTopology();
    },

    handleStatusFilter(value) {
      this.selectedStatus = value;
      this.filterAndRenderTopology();
    },

    handleTypeFilter(value) {
      this.selectedType = value;
      this.filterAndRenderTopology();
    },

    changeLayout(layout) {
      this.layoutType = layout;
      this.$message.success(`已切换到${this.getLayoutLabel(layout)}布局`);
      this.renderTopology();
    },

    getLayoutLabel(layout) {
      const layoutMap = {
        force: "力导向图",
        circular: "环形布局",
        grid: "网格布局",
        hierarchy: "分层布局",
      };
      return layoutMap[layout] || layout;
    },

    // 视图控制
    zoomIn() {
      if (this.topologyChart) {
        this.topologyChart.dispatchAction({
          type: "dataZoom",
          start: 10,
          end: 90,
        });
        this.$message.success("已放大视图");
      }
    },

    zoomOut() {
      if (this.topologyChart) {
        this.topologyChart.dispatchAction({
          type: "dataZoom",
          start: 0,
          end: 100,
        });
        this.$message.success("已缩小视图");
      }
    },

    resetView() {
      if (this.topologyChart) {
        this.topologyChart.dispatchAction({
          type: "restore",
        });
        this.renderTopology();
        this.$message.success("已重置视图");
      }
    },

    fullscreen() {
      const element = this.$el.querySelector(".topology-canvas");
      if (element) {
        if (element.requestFullscreen) {
          element.requestFullscreen();
        } else if (element.webkitRequestFullscreen) {
          element.webkitRequestFullscreen();
        } else if (element.mozRequestFullScreen) {
          element.mozRequestFullScreen();
        } else if (element.msRequestFullscreen) {
          element.msRequestFullscreen();
        }
        this.$message.success("已进入全屏模式");
      }
    },

    // 数据操作
    refreshData() {
      this.loadDeviceData();
    },

    exportData() {
      if (this.topologyChart) {
        const url = this.topologyChart.getDataURL({
          type: "png",
          backgroundColor: "#1a1a1a",
        });
        const link = document.createElement("a");
        link.download = `network-topology-${new Date().getTime()}.png`;
        link.href = url;
        link.click();
      }
    },

    // 批量操作
    batchRestart() {
      this.batchOperation("重启");
    },

    batchShutdown() {
      this.batchOperation("关机");
    },

    batchMonitor() {
      this.batchOperation("监控");
    },

    batchOperation(operation) {
      const deviceNames = this.selectedDevices.map((d) => d.name).join(", ");
      this.$confirm({
        title: `批量${operation}`,
        content: `确定要对以下设备执行${operation}操作吗？\n${deviceNames}`,
        onOk: () => {
          this.$message.success(
            `已对 ${this.selectedDevices.length} 台设备执行${operation}操作`
          );
          this.clearSelection();
        },
      });
    },

    clearSelection() {
      this.selectedDevices = [];
      this.renderTopology();
    },

    // 设备操作
    restartDevice() {
      this.deviceOperation("重启");
    },

    shutdownDevice() {
      this.deviceOperation("关机");
    },

    monitorDevice() {
      this.deviceOperation("监控");
    },

    configDevice() {
      this.deviceOperation("配置");
    },

    deviceOperation(operation) {
      this.$confirm({
        title: `${operation}设备`,
        content: `确定要${operation}设备 ${this.selectedDevice.name} 吗？`,
        onOk: () => {
          this.$message.success(
            `已对设备 ${this.selectedDevice.name} 执行${operation}操作`
          );
        },
      });
    },

    // 告警处理
    handleAlert(alert) {
      this.$confirm({
        title: "处理告警",
        content: `确定要处理告警"${alert.title}"吗？`,
        okText: "确定",
        cancelText: "取消",
        onOk: () => {
          // 标记告警为已处理
          const index = this.alerts.findIndex((a) => a.id === alert.id);
          if (index > -1) {
            this.alerts[index].status = "handled";
            this.alerts[index].handledTime = new Date().toLocaleString();
            this.$message.success("告警处理完成");

            // 3秒后移除已处理的告警
            setTimeout(() => {
              const currentIndex = this.alerts.findIndex(
                (a) => a.id === alert.id
              );
              if (currentIndex > -1) {
                this.alerts.splice(currentIndex, 1);
              }
            }, 3000);
          }
        },
      });
    },

    // 清除所有告警
    clearAllAlerts() {
      this.$confirm({
        title: "清除所有告警",
        content: "确定要清除所有告警吗？此操作不可撤销。",
        okText: "确定",
        cancelText: "取消",
        type: "warning",
        onOk: () => {
          this.alerts = [];
          this.$message.success("已清除所有告警");
        },
      });
    },

    // 导出告警日志
    exportAlerts() {
      const alertData = this.alerts.map((alert) => ({
        时间: alert.time,
        级别: alert.level,
        标题: alert.title,
        设备: alert.device,
        状态: alert.status || "未处理",
      }));

      const csvContent = this.convertToCSV(alertData);
      const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
      const link = document.createElement("a");
      const url = URL.createObjectURL(blob);
      link.setAttribute("href", url);
      link.setAttribute(
        "download",
        `告警日志_${new Date().toISOString().slice(0, 10)}.csv`
      );
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      this.$message.success("告警日志导出成功");
    },

    // 转换为CSV格式
    convertToCSV(data) {
      if (!data.length) return "";

      const headers = Object.keys(data[0]);
      const csvHeaders = headers.join(",");
      const csvRows = data.map((row) =>
        headers.map((header) => `"${row[header] || ""}"`).join(",")
      );

      return [csvHeaders, ...csvRows].join("\n");
    },
  },
};
</script>

<style scoped lang="less">
.dark-topology-container {
  height: 100vh;
  background: linear-gradient(135deg, #0c0c0c 0%, #1a1a1a 50%, #0c0c0c 100%);
  color: #ffffff;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  // 顶部导航栏
  .top-navbar {
    height: 60px;
    background: linear-gradient(90deg, #1a1a1a 0%, #2a2a2a 100%);
    border-bottom: 1px solid #333333;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);

    .navbar-left {
      display: flex;
      align-items: center;
      gap: 32px;

      .logo-section {
        display: flex;
        align-items: center;
        gap: 12px;

        .logo-icon {
          font-size: 24px;
          color: #00d4ff;
        }

        .logo-text {
          font-size: 18px;
          font-weight: bold;
          background: linear-gradient(45deg, #00d4ff, #00ff88);
          -webkit-background-clip: text;
          background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }

      .nav-tabs {
        display: flex;
        gap: 8px;

        .nav-tab {
          padding: 8px 16px;
          border-radius: 6px;
          cursor: pointer;
          transition: all 0.3s ease;
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 14px;

          &:hover {
            background: rgba(0, 212, 255, 0.1);
            color: #00d4ff;
          }

          &.active {
            background: linear-gradient(45deg, #00d4ff, #00ff88);
            color: #000000;
            font-weight: bold;
          }
        }
      }
    }

    .navbar-right {
      display: flex;
      align-items: center;
      gap: 24px;

      .system-status {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;

        .status-indicator {
          width: 8px;
          height: 8px;
          border-radius: 50%;

          &.online {
            background: #00ff88;
            box-shadow: 0 0 8px #00ff88;
          }
        }
      }

      .time-display {
        font-family: "Courier New", monospace;
        font-size: 14px;
        color: #00d4ff;
        background: rgba(0, 212, 255, 0.1);
        padding: 4px 12px;
        border-radius: 4px;
        border: 1px solid rgba(0, 212, 255, 0.3);
      }

      .user-menu {
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
        padding: 4px 12px;
        border-radius: 6px;
        transition: all 0.3s ease;

        &:hover {
          background: rgba(255, 255, 255, 0.1);
        }
      }
    }
  }

  // 主要内容区域
  .main-content {
    flex: 1;
    display: flex;
    gap: 16px;
    padding: 16px;
    overflow: hidden;

    // 左侧监控面板
    .monitoring-panel {
      width: 320px;
      background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
      border-radius: 12px;
      border: 1px solid #333333;
      overflow-y: auto;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);

      h3 {
        margin: 0 0 16px 0;
        font-size: 16px;
        font-weight: bold;
        color: #ffffff;
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .stats-section,
      .alerts-section,
      .performance-section {
        padding: 20px;
        border-bottom: 1px solid #333333;

        &:last-child {
          border-bottom: none;
        }
      }

      // 统计网格
      .stats-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 12px;

        .stat-item {
          background: rgba(0, 0, 0, 0.3);
          border-radius: 8px;
          padding: 16px;
          border: 1px solid #333333;
          transition: all 0.3s ease;

          &:hover {
            border-color: #00d4ff;
            box-shadow: 0 0 12px rgba(0, 212, 255, 0.2);
          }

          .stat-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8px;
            font-size: 16px;

            &.online {
              background: linear-gradient(45deg, #00ff88, #00d4ff);
              color: #000000;
            }

            &.offline {
              background: linear-gradient(45deg, #666666, #999999);
              color: #ffffff;
            }

            &.warning {
              background: linear-gradient(45deg, #ffaa00, #ff6b00);
              color: #000000;
            }

            &.error {
              background: linear-gradient(45deg, #ff0040, #ff4d4f);
              color: #ffffff;
            }
          }

          .stat-content {
            .stat-value {
              font-size: 24px;
              font-weight: bold;
              line-height: 1;
              margin-bottom: 4px;
            }

            .stat-label {
              font-size: 12px;
              color: #999999;
              margin-bottom: 4px;
            }

            .stat-trend {
              font-size: 11px;
              display: flex;
              align-items: center;
              gap: 4px;

              &.up {
                color: #00ff88;
              }

              &.down {
                color: #ff0040;
              }
            }
          }
        }
      }

      // 告警列表
      .alerts-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        h3 {
          margin: 0;
        }

        .alerts-actions {
          display: flex;
          gap: 8px;

          .ant-btn {
            color: #00d4ff;
            border-color: transparent;

            &:hover {
              background: rgba(0, 212, 255, 0.1);
              color: #00d4ff;
            }
          }
        }
      }

      .alerts-list {
        max-height: 200px;
        overflow-y: auto;

        .alert-item {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 12px;
          margin-bottom: 8px;
          background: rgba(0, 0, 0, 0.2);
          border-radius: 6px;
          border-left: 4px solid;
          transition: all 0.3s ease;

          &.critical {
            border-left-color: #ff0040;
          }

          &.warning {
            border-left-color: #ffaa00;
          }

          &.info {
            border-left-color: #00d4ff;
          }

          &.handled {
            opacity: 0.6;
            background: rgba(0, 0, 0, 0.1);
          }

          &:hover {
            background: rgba(0, 0, 0, 0.4);
          }

          .alert-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: currentColor;
          }

          .alert-content {
            flex: 1;

            .alert-title {
              font-size: 13px;
              font-weight: 500;
              margin-bottom: 2px;
            }

            .alert-device {
              font-size: 12px;
              color: #00d4ff;
              margin-bottom: 2px;
            }

            .alert-time {
              font-size: 11px;
              color: #999999;
            }

            .alert-handled {
              font-size: 10px;
              color: #00ff88;
              margin-top: 2px;
            }
          }

          .alert-actions {
            .ant-btn {
              color: #00d4ff;
              border-color: #00d4ff;

              &:hover {
                background: rgba(0, 212, 255, 0.1);
              }
            }

            .handled-badge {
              font-size: 10px;
              color: #00ff88;
              background: rgba(0, 255, 136, 0.1);
              padding: 2px 6px;
              border-radius: 4px;
              border: 1px solid #00ff88;
            }
          }
        }

        .no-alerts {
          text-align: center;
          padding: 20px;
          color: #666666;

          .anticon {
            font-size: 24px;
            color: #00ff88;
            margin-bottom: 8px;
          }

          p {
            margin: 0;
            font-size: 12px;
          }
        }
      }

      // 性能指标
      .performance-metrics {
        .metric-item {
          margin-bottom: 16px;

          &:last-child {
            margin-bottom: 0;
          }

          .metric-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 6px;

            .metric-name {
              font-size: 13px;
              color: #ffffff;
            }

            .metric-value {
              font-size: 13px;
              font-weight: bold;
              color: #00d4ff;
            }
          }

          .metric-bar {
            height: 6px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 3px;
            overflow: hidden;

            .metric-fill {
              height: 100%;
              border-radius: 3px;
              transition: all 0.3s ease;

              &.normal {
                background: linear-gradient(90deg, #00ff88, #00d4ff);
              }

              &.warning {
                background: linear-gradient(90deg, #ffaa00, #ff6b00);
              }

              &.critical {
                background: linear-gradient(90deg, #ff0040, #ff4d4f);
              }
            }
          }
        }
      }
    }

    // 中央内容区域
    .main-content-area {
      flex: 1;
      display: flex;
      flex-direction: column;
      height: 100%;
      overflow: hidden;
    }

    // 中央拓扑区域
    .topology-main {
      flex: 1;
      background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
      border-radius: 12px;
      border: 1px solid #333333;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);

      // 控制工具栏
      .control-toolbar {
        padding: 16px 20px;
        border-bottom: 1px solid #333333;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: rgba(0, 0, 0, 0.2);

        .toolbar-left {
          display: flex;
          align-items: center;
          gap: 16px;

          .search-box {
            .ant-input-search {
              .ant-input {
                background: rgba(0, 0, 0, 0.3);
                border-color: #333333;
                color: #ffffff;

                &:focus {
                  border-color: #00d4ff;
                  box-shadow: 0 0 0 2px rgba(0, 212, 255, 0.2);
                }

                &::placeholder {
                  color: #666666;
                }
              }

              .ant-input-search-button {
                background: #00d4ff;
                border-color: #00d4ff;

                &:hover {
                  background: #00b8e6;
                }
              }
            }
          }

          .filter-controls {
            display: flex;
            gap: 12px;

            .ant-select {
              .ant-select-selector {
                background: rgba(0, 0, 0, 0.3) !important;
                border-color: #333333 !important;
                color: #ffffff !important;
              }

              &.ant-select-focused .ant-select-selector {
                border-color: #00d4ff !important;
                box-shadow: 0 0 0 2px rgba(0, 212, 255, 0.2) !important;
              }
            }
          }
        }

        .toolbar-right {
          display: flex;
          align-items: center;
          gap: 16px;

          .layout-switcher,
          .view-controls,
          .action-controls {
            .ant-btn {
              background: rgba(0, 0, 0, 0.3);
              border-color: #333333;
              color: #ffffff;

              &:hover {
                background: rgba(0, 212, 255, 0.1);
                border-color: #00d4ff;
                color: #00d4ff;
              }

              &.ant-btn-primary {
                background: linear-gradient(45deg, #00d4ff, #00ff88);
                border-color: transparent;
                color: #000000;

                &:hover {
                  background: linear-gradient(45deg, #00b8e6, #00e67a);
                }
              }

              &.ant-btn-ghost {
                background: transparent;
                border-color: #333333;
                color: #ffffff;

                &:hover {
                  background: rgba(0, 212, 255, 0.1);
                  border-color: #00d4ff;
                  color: #00d4ff;
                }
              }
            }
          }
        }
      }

      // 拓扑图画布
      .topology-canvas {
        flex: 1;
        position: relative;
        background: radial-gradient(circle at center, #1a1a1a 0%, #0c0c0c 100%);

        .chart-container {
          width: 100%;
          height: 100%;
        }

        // 迷你地图
        .minimap {
          position: absolute;
          top: 20px;
          right: 20px;
          width: 220px;
          height: 180px;
          background: rgba(0, 0, 0, 0.9);
          border: 1px solid #333333;
          border-radius: 8px;
          overflow: hidden;
          backdrop-filter: blur(10px);

          .minimap-header {
            padding: 8px 12px;
            background: rgba(0, 212, 255, 0.1);
            border-bottom: 1px solid #333333;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #00d4ff;

            .minimap-controls {
              display: flex;
              gap: 4px;

              .ant-btn {
                color: #00d4ff;
                border: none;
                background: transparent;

                &:hover {
                  background: rgba(0, 212, 255, 0.2);
                  color: #00d4ff;
                }
              }
            }
          }

          .minimap-content {
            height: calc(100% - 32px);
            background: #0c0c0c;
            padding: 8px;

            .minimap-overview {
              .overview-stats {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 8px;
                height: 100%;

                .stat-item {
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  justify-content: center;
                  background: rgba(0, 0, 0, 0.3);
                  border-radius: 4px;
                  padding: 8px 4px;

                  .stat-label {
                    font-size: 10px;
                    color: #999999;
                    margin-bottom: 4px;
                  }

                  .stat-value {
                    font-size: 14px;
                    font-weight: bold;
                    color: #ffffff;

                    &.online {
                      color: #00ff88;
                    }

                    &.warning {
                      color: #ffaa00;
                    }

                    &.error {
                      color: #ff0040;
                    }
                  }
                }
              }
            }

            .minimap-topology {
              display: grid;
              grid-template-columns: repeat(5, 1fr);
              gap: 4px;
              height: 100%;
              align-content: start;

              .mini-device {
                width: 20px;
                height: 20px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                transition: all 0.3s ease;

                &:hover {
                  transform: scale(1.2);
                }

                .mini-device-dot {
                  width: 8px;
                  height: 8px;
                  border-radius: 50%;
                  background: currentColor;
                }

                &.server {
                  background: rgba(0, 212, 255, 0.2);
                  border: 1px solid #00d4ff;
                  color: #00d4ff;

                  &.online {
                    background: rgba(0, 212, 255, 0.3);
                  }

                  &.warning {
                    background: rgba(255, 170, 0, 0.3);
                    border-color: #ffaa00;
                    color: #ffaa00;
                  }

                  &.error {
                    background: rgba(255, 0, 64, 0.3);
                    border-color: #ff0040;
                    color: #ff0040;
                  }

                  &.offline {
                    background: rgba(102, 102, 102, 0.3);
                    border-color: #666666;
                    color: #666666;
                  }
                }

                &.switch {
                  background: rgba(0, 255, 136, 0.2);
                  border: 1px solid #00ff88;
                  color: #00ff88;

                  &.warning {
                    background: rgba(255, 170, 0, 0.3);
                    border-color: #ffaa00;
                    color: #ffaa00;
                  }

                  &.error {
                    background: rgba(255, 0, 64, 0.3);
                    border-color: #ff0040;
                    color: #ff0040;
                  }

                  &.offline {
                    background: rgba(102, 102, 102, 0.3);
                    border-color: #666666;
                    color: #666666;
                  }
                }

                &.router {
                  background: rgba(255, 107, 0, 0.2);
                  border: 1px solid #ff6b00;
                  color: #ff6b00;

                  &.warning {
                    background: rgba(255, 170, 0, 0.3);
                    border-color: #ffaa00;
                    color: #ffaa00;
                  }

                  &.error {
                    background: rgba(255, 0, 64, 0.3);
                    border-color: #ff0040;
                    color: #ff0040;
                  }

                  &.offline {
                    background: rgba(102, 102, 102, 0.3);
                    border-color: #666666;
                    color: #666666;
                  }
                }

                &.firewall {
                  background: rgba(255, 0, 64, 0.2);
                  border: 1px solid #ff0040;
                  color: #ff0040;

                  &.warning {
                    background: rgba(255, 170, 0, 0.3);
                    border-color: #ffaa00;
                    color: #ffaa00;
                  }

                  &.offline {
                    background: rgba(102, 102, 102, 0.3);
                    border-color: #666666;
                    color: #666666;
                  }
                }

                &.storage {
                  background: rgba(128, 0, 255, 0.2);
                  border: 1px solid #8000ff;
                  color: #8000ff;

                  &.warning {
                    background: rgba(255, 170, 0, 0.3);
                    border-color: #ffaa00;
                    color: #ffaa00;
                  }

                  &.error {
                    background: rgba(255, 0, 64, 0.3);
                    border-color: #ff0040;
                    color: #ff0040;
                  }

                  &.offline {
                    background: rgba(102, 102, 102, 0.3);
                    border-color: #666666;
                    color: #666666;
                  }
                }
              }
            }
          }
        }

        // 选择工具栏
        .selection-toolbar {
          position: absolute;
          bottom: 20px;
          left: 50%;
          transform: translateX(-50%);
          background: rgba(0, 0, 0, 0.9);
          border: 1px solid #333333;
          border-radius: 8px;
          padding: 12px 16px;
          display: flex;
          align-items: center;
          gap: 16px;
          backdrop-filter: blur(10px);

          .selection-info {
            font-size: 14px;
            color: #00d4ff;
            font-weight: 500;
          }

          .selection-actions {
            display: flex;
            gap: 8px;

            .ant-btn {
              background: rgba(0, 0, 0, 0.5);
              border-color: #333333;
              color: #ffffff;
              font-size: 12px;

              &:hover {
                background: rgba(0, 212, 255, 0.1);
                border-color: #00d4ff;
                color: #00d4ff;
              }
            }
          }
        }
      }
    }

    // 右侧详情面板
    .details-panel {
      width: 320px;
      background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
      border-radius: 12px;
      border: 1px solid #333333;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
      transition: all 0.3s ease;

      &.collapsed {
        width: 60px;
      }

      .panel-header {
        padding: 16px 20px;
        border-bottom: 1px solid #333333;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: rgba(0, 0, 0, 0.2);
        font-weight: bold;
        color: #ffffff;

        .ant-btn {
          background: transparent;
          border: none;
          color: #ffffff;

          &:hover {
            background: rgba(0, 212, 255, 0.1);
            color: #00d4ff;
          }
        }
      }

      .panel-content {
        flex: 1;
        padding: 20px;
        overflow-y: auto;

        .device-details {
          .device-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 24px;
            padding: 16px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            border: 1px solid #333333;

            .device-icon {
              width: 48px;
              height: 48px;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 20px;
              color: #000000;

              &.server {
                background: linear-gradient(45deg, #00d4ff, #00b8e6);
              }

              &.switch {
                background: linear-gradient(45deg, #00ff88, #00e67a);
              }

              &.router {
                background: linear-gradient(45deg, #ff6b00, #ff8533);
              }

              &.firewall {
                background: linear-gradient(45deg, #ff0040, #ff3366);
              }

              &.storage {
                background: linear-gradient(45deg, #8000ff, #9933ff);
              }
            }

            .device-info {
              flex: 1;

              h4 {
                margin: 0 0 4px 0;
                font-size: 16px;
                color: #ffffff;
              }

              p {
                margin: 0;
                font-size: 12px;
                color: #999999;
              }
            }

            .device-status {
              padding: 4px 8px;
              border-radius: 4px;
              font-size: 11px;
              font-weight: bold;

              &.online {
                background: rgba(0, 255, 136, 0.2);
                color: #00ff88;
                border: 1px solid #00ff88;
              }

              &.offline {
                background: rgba(102, 102, 102, 0.2);
                color: #666666;
                border: 1px solid #666666;
              }

              &.warning {
                background: rgba(255, 170, 0, 0.2);
                color: #ffaa00;
                border: 1px solid #ffaa00;
              }

              &.error {
                background: rgba(255, 0, 64, 0.2);
                color: #ff0040;
                border: 1px solid #ff0040;
              }
            }
          }

          .info-section,
          .metrics-section,
          .actions-section {
            margin-bottom: 24px;

            h5 {
              margin: 0 0 12px 0;
              font-size: 14px;
              color: #00d4ff;
              font-weight: bold;
            }
          }

          .info-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 8px;

            .info-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 8px 0;
              border-bottom: 1px solid #333333;

              &:last-child {
                border-bottom: none;
              }

              label {
                font-size: 12px;
                color: #999999;
              }

              span {
                font-size: 12px;
                color: #ffffff;
                font-family: "Courier New", monospace;
              }
            }
          }

          .metrics-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 12px;

            .metric-card {
              background: rgba(0, 0, 0, 0.3);
              border: 1px solid #333333;
              border-radius: 6px;
              padding: 12px;

              .metric-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;
                font-size: 12px;

                .metric-value {
                  color: #00d4ff;
                  font-weight: bold;
                }
              }

              .metric-progress {
                height: 4px;
                background: rgba(0, 0, 0, 0.5);
                border-radius: 2px;
                overflow: hidden;

                .progress-bar {
                  height: 100%;
                  border-radius: 2px;
                  transition: all 0.3s ease;

                  &.normal {
                    background: linear-gradient(90deg, #00ff88, #00d4ff);
                  }

                  &.warning {
                    background: linear-gradient(90deg, #ffaa00, #ff6b00);
                  }

                  &.critical {
                    background: linear-gradient(90deg, #ff0040, #ff4d4f);
                  }
                }
              }
            }
          }

          .action-buttons {
            display: grid;
            grid-template-columns: 1fr;
            gap: 8px;

            .ant-btn {
              background: rgba(0, 0, 0, 0.3);
              border-color: #333333;
              color: #ffffff;
              text-align: left;

              &:hover {
                background: rgba(0, 212, 255, 0.1);
                border-color: #00d4ff;
                color: #00d4ff;
              }
            }
          }
        }

        .no-selection {
          text-align: center;
          padding: 40px 20px;
          color: #666666;

          .anticon {
            font-size: 48px;
            margin-bottom: 16px;
            color: #333333;
          }

          p {
            margin: 0;
            font-size: 14px;
          }
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 1600px) {
    .main-content {
      .monitoring-panel,
      .details-panel {
        width: 280px;
      }
    }
  }

  @media (max-width: 1200px) {
    .main-content {
      flex-direction: column;
      gap: 12px;

      .monitoring-panel,
      .details-panel {
        width: 100%;
        height: auto;
        max-height: 300px;

        &.collapsed {
          width: 100%;
          height: 60px;
        }
      }

      .topology-main {
        height: 500px;
      }
    }
  }

  @media (max-width: 768px) {
    .top-navbar {
      flex-direction: column;
      height: auto;
      padding: 12px;
      gap: 12px;

      .navbar-left {
        gap: 16px;

        .nav-tabs {
          flex-wrap: wrap;
        }
      }

      .navbar-right {
        gap: 12px;
        font-size: 12px;
      }
    }

    .main-content {
      padding: 12px;

      .topology-main {
        .control-toolbar {
          flex-direction: column;
          gap: 12px;
          padding: 12px;

          .toolbar-left,
          .toolbar-right {
            width: 100%;
            justify-content: center;
            flex-wrap: wrap;
          }
        }

        .topology-canvas {
          .selection-toolbar {
            flex-direction: column;
            gap: 8px;
            padding: 8px 12px;

            .selection-actions {
              flex-wrap: wrap;
              justify-content: center;
            }
          }

          .minimap {
            width: 150px;
            height: 100px;
            top: 10px;
            right: 10px;
          }
        }
      }
    }
  }
}

// 全局暗黑主题覆盖
.dark-topology-container {
  .ant-select-dropdown {
    background: #1a1a1a !important;
    border-color: #333333 !important;

    .ant-select-item {
      color: #ffffff !important;

      &:hover {
        background: rgba(0, 212, 255, 0.1) !important;
      }

      &.ant-select-item-option-selected {
        background: rgba(0, 212, 255, 0.2) !important;
        color: #00d4ff !important;
      }
    }
  }

  .ant-modal {
    .ant-modal-content {
      background: #1a1a1a !important;
      color: #ffffff !important;
    }

    .ant-modal-header {
      background: #1a1a1a !important;
      border-bottom-color: #333333 !important;

      .ant-modal-title {
        color: #ffffff !important;
      }
    }
  }

  .ant-message {
    .ant-message-notice-content {
      background: #1a1a1a !important;
      color: #ffffff !important;
      border: 1px solid #333333 !important;
    }
  }

  // 性能监控标签页
  .performance-main {
    flex: 1;
    background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
    border-radius: 12px;
    border: 1px solid #333333;
    padding: 24px;
    overflow-y: auto;

    .performance-header {
      margin-bottom: 32px;
      text-align: center;

      h2 {
        color: #ffffff;
        font-size: 24px;
        margin-bottom: 8px;

        .anticon {
          margin-right: 12px;
          color: #00d4ff;
        }
      }

      p {
        color: #999999;
        font-size: 14px;
        margin: 0;
      }
    }

    .performance-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 24px;

      .performance-card {
        background: rgba(0, 0, 0, 0.3);
        border-radius: 12px;
        padding: 20px;
        border: 1px solid #333333;
        transition: all 0.3s ease;

        &:hover {
          border-color: #00d4ff;
          box-shadow: 0 0 20px rgba(0, 212, 255, 0.2);
        }

        &.warning {
          border-color: #ffaa00;
          background: rgba(255, 170, 0, 0.1);
        }

        .metric-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;

          .metric-name {
            color: #ffffff;
            font-size: 16px;
            font-weight: 500;
          }

          .metric-value {
            color: #00d4ff;
            font-size: 20px;
            font-weight: bold;
          }
        }

        .metric-progress {
          margin-bottom: 12px;
        }

        .metric-threshold {
          color: #999999;
          font-size: 12px;
          text-align: right;
        }
      }
    }
  }

  // 告警中心标签页
  .alerts-main {
    flex: 1;
    background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
    border-radius: 12px;
    border: 1px solid #333333;
    padding: 24px;
    overflow-y: auto;

    .alerts-header-main {
      margin-bottom: 32px;

      h2 {
        color: #ffffff;
        font-size: 24px;
        margin-bottom: 16px;

        .anticon {
          margin-right: 12px;
          color: #ff0040;
        }
      }

      .alerts-summary {
        display: flex;
        gap: 24px;

        .summary-item {
          background: rgba(0, 0, 0, 0.3);
          border-radius: 8px;
          padding: 16px 20px;
          border: 1px solid #333333;
          text-align: center;
          min-width: 120px;

          .count {
            display: block;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 4px;
          }

          .label {
            font-size: 12px;
            color: #999999;
          }

          &.critical {
            border-color: #ff0040;
            .count {
              color: #ff0040;
            }
          }

          &.warning {
            border-color: #ffaa00;
            .count {
              color: #ffaa00;
            }
          }

          &.info {
            border-color: #00d4ff;
            .count {
              color: #00d4ff;
            }
          }
        }
      }
    }

    .alerts-table {
      background: rgba(0, 0, 0, 0.3);
      border-radius: 8px;
      border: 1px solid #333333;

      .handled-text {
        color: #00ff88;
        font-size: 12px;
      }
    }
  }
}
</style>
