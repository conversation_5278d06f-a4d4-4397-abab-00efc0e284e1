<template>
  <div class="home">
    <div class="header">
      <h1 class="title">系统管理中心</h1>
      <p class="subtitle">选择您需要的功能模块</p>
    </div>

    <div class="function-grid">
      <div
        v-for="item in functionItems"
        :key="item.id"
        class="function-card"
        @click="navigateTo(item.route)"
      >
        <div class="icon-container">
          <i :class="item.icon"></i>
        </div>
        <h3 class="function-title">{{ item.name }}</h3>
        <p class="function-desc">{{ item.description }}</p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "HomeView",
  data() {
    return {
      functionItems: [
        {
          id: 1,
          name: "IP 监控",
          description: "监控网络IP状态和连接情况",
          icon: "fas fa-network-wired",
          route: "/ip-monitor",
        },
        {
          id: 2,
          name: "系统监控",
          description: "查看系统资源使用情况",
          icon: "fas fa-desktop",
          route: "/system-monitor",
        },
        {
          id: 3,
          name: "网络分析",
          description: "分析网络流量和性能",
          icon: "fas fa-chart-line",
          route: "/network-analysis",
        },
        {
          id: 4,
          name: "日志管理",
          description: "查看和管理系统日志",
          icon: "fas fa-file-alt",
          route: "/log-management",
        },
        {
          id: 5,
          name: "用户管理",
          description: "管理系统用户和权限",
          icon: "fas fa-users",
          route: "/user-management",
        },
        {
          id: 6,
          name: "设置配置",
          description: "系统设置和配置管理",
          icon: "fas fa-cog",
          route: "/settings",
        },
      ],
    };
  },
  methods: {
    navigateTo(route) {
      this.$router.push(route);
    },
  },
};
</script>

<style scoped>
.home {
  padding: 40px 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  text-align: center;
  margin-bottom: 50px;
}

.title {
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 10px;
  font-weight: 600;
}

.subtitle {
  font-size: 1.2rem;
  color: #7f8c8d;
  margin: 0;
}

.function-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-top: 40px;
}

.function-card {
  background: #ffffff;
  border-radius: 15px;
  padding: 30px 20px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 2px solid transparent;
}

.function-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  border-color: #3498db;
}

.icon-container {
  width: 80px;
  height: 80px;
  margin: 0 auto 20px;
  background: linear-gradient(135deg, #3498db, #2980b9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.function-card:hover .icon-container {
  background: linear-gradient(135deg, #2980b9, #1f4e79);
  transform: scale(1.1);
}

.icon-container i {
  font-size: 2rem;
  color: white;
}

.function-title {
  font-size: 1.4rem;
  color: #2c3e50;
  margin: 0 0 10px 0;
  font-weight: 600;
}

.function-desc {
  font-size: 1rem;
  color: #7f8c8d;
  margin: 0;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .function-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .title {
    font-size: 2rem;
  }

  .function-card {
    padding: 25px 15px;
  }
}
</style>
