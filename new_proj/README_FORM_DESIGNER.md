# 自定义表单设计器

## 功能概述

我们为Vue项目创建了一个功能强大的自定义表单设计器，位于"系统监控"页面。这个设计器允许用户通过拖拽的方式创建自定义表单。

## 主要功能

### 1. 拖拽式表单设计
- **左侧字段库**：包含多种预定义字段类型
- **中间设计画布**：拖拽字段到此区域进行表单设计
- **右侧属性面板**：配置选中字段的属性

### 2. 支持的字段类型
- **源IP/目标IP**：专门用于IP地址输入的字段
- **临时有效期/开通时间**：日期范围选择器
- **用途**：多行文本输入框
- **单行文本**：普通文本输入
- **数字**：数字输入框
- **下拉选择**：可配置选项的下拉框
- **日期**：日期选择器

### 3. 字段操作功能
- **上移/下移**：调整字段在表单中的位置
- **复制**：快速复制现有字段
- **删除**：移除不需要的字段
- **属性配置**：
  - 字段标签
  - 字段名称
  - 占位符文本
  - 是否必填
  - 字段宽度（25%、33%、50%、75%、100%）

### 4. 表单管理功能
- **表单预览**：实时预览设计的表单效果
- **保存表单**：将设计好的表单保存到本地存储
- **加载表单**：从已保存的表单中选择并加载
- **复制表单**：快速复制现有表单进行修改
- **删除表单**：删除不需要的表单
- **清空表单**：清空当前设计的表单

## 技术实现

### 组件结构
```
SystemMonitorView.vue (主页面)
├── FormDesigner.vue (表单设计器组件)
    ├── 左侧字段库 (draggable)
    ├── 中间设计画布 (draggable)
    ├── 右侧属性面板
    └── 表单预览模态框
```

### 核心技术
- **Vue 3 Composition API**：现代Vue开发模式
- **Ant Design Vue**：UI组件库
- **vuedraggable**：拖拽功能实现
- **localStorage**：本地数据持久化
- **响应式设计**：支持不同屏幕尺寸

### 数据结构
```javascript
// 表单数据结构
{
  id: 唯一标识,
  name: "表单名称",
  fields: [
    {
      id: "字段唯一标识",
      type: "字段类型",
      label: "字段标签",
      name: "字段名称",
      placeholder: "占位符",
      required: false,
      width: "100%",
      // 其他特定属性...
    }
  ],
  createTime: "创建时间",
  updateTime: "更新时间"
}
```

## 使用方法

### 1. 创建新表单
1. 点击主页的"系统监控"图标进入表单设计器
2. 从左侧字段库拖拽所需字段到中间画布
3. 点击字段进行属性配置
4. 输入表单名称
5. 点击"保存"按钮

### 2. 编辑现有表单
1. 在页面底部的"已保存的表单"区域点击要编辑的表单
2. 表单会自动加载到设计器中
3. 进行修改后重新保存

### 3. 预览表单
- 点击"预览"按钮查看表单的实际效果
- 预览模式下可以看到所有字段的布局和样式

### 4. 管理表单
- **复制表单**：点击表单卡片上的"复制"按钮
- **删除表单**：点击表单卡片上的"删除"按钮
- **清空当前表单**：点击设计器中的"清空"按钮

## 特色功能

### 1. 专业的IP地址字段
- 专门为网络监控场景设计
- 带有"IP"前缀标识
- 支持IPv4地址格式验证

### 2. 灵活的日期范围选择
- 支持临时有效期和开通时间等业务场景
- 标准的日期范围选择器
- 格式化显示

### 3. 智能的字段属性配置
- 根据字段类型显示相应的配置选项
- 下拉选择框支持动态添加/删除选项
- 实时预览配置效果

### 4. 响应式设计
- 支持桌面端和移动端
- 自适应布局
- 优雅的交互动画

## 扩展性

### 添加新字段类型
1. 在`availableFields`数组中添加新字段定义
2. 在`getFieldComponent`方法中添加组件映射
3. 在`getFieldProps`方法中添加属性处理逻辑
4. 根据需要在属性面板中添加特殊配置

### 自定义样式
- 所有样式都使用scoped CSS，便于定制
- 支持主题色彩配置
- 响应式断点可调整

## 数据持久化

表单数据保存在浏览器的localStorage中，键名为`customForms`。数据格式为JSON数组，包含所有已保存的表单。

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 注意事项

1. 表单数据保存在本地存储中，清除浏览器数据会丢失已保存的表单
2. 建议定期导出重要的表单配置
3. 字段名称应保持唯一性，避免数据冲突
4. 大型表单可能影响性能，建议合理控制字段数量

## 未来扩展计划

1. **数据验证**：添加字段级别的数据验证规则
2. **表单导入导出**：支持JSON格式的表单配置导入导出
3. **模板库**：预置常用表单模板
4. **条件显示**：支持字段间的条件显示逻辑
5. **数据绑定**：与后端API集成，支持数据提交和回显
6. **权限控制**：不同用户角色的表单设计权限
7. **版本管理**：表单配置的版本控制和回滚功能
